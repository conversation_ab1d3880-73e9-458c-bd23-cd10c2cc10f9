import React, { useState, useCallback, useMemo } from 'react';
import DataGridComponent, { 
  Column, 
  SortColumn, 
  SelectColumn,
  textEditor,
  Row
} from 'react-data-grid';
import { useThemeStyles } from '../../services/theme';
import './DataGrid.css';

export interface DataGridColumn<T = any> {
  key: string;
  name: string;
  width?: number;
  minWidth?: number;
  maxWidth?: number;
  resizable?: boolean;
  sortable?: boolean;
  filterable?: boolean;
  editable?: boolean;
  renderCell?: (props: { row: T; column: DataGridColumn<T> }) => React.ReactNode;
  renderHeaderCell?: (props: { column: DataGridColumn<T> }) => React.ReactNode;
  formatter?: (props: { row: T; column: DataGridColumn<T> }) => React.ReactNode;
  editor?: React.ComponentType<any>;
  cellClass?: string | ((row: T) => string);
  headerCellClass?: string;
}

export interface DataGridProps<T = any> {
  columns: DataGridColumn<T>[];
  rows: T[];
  onRowsChange?: (rows: T[]) => void;
  onRowClick?: (row: T, index: number) => void;
  onRowDoubleClick?: (row: T, index: number) => void;
  selectedRows?: Set<string>;
  onSelectedRowsChange?: (selectedRows: Set<string>) => void;
  rowKeyGetter?: (row: T) => string;
  sortColumns?: SortColumn[];
  onSortColumnsChange?: (sortColumns: SortColumn[]) => void;
  defaultColumnOptions?: Partial<Column<T>>;
  className?: string;
  style?: React.CSSProperties;
  headerRowHeight?: number;
  rowHeight?: number;
  enableVirtualization?: boolean;
  enableFiltering?: boolean;
  enableSorting?: boolean;
  enableSelection?: boolean;
  loading?: boolean;
  emptyRowsRenderer?: () => React.ReactNode;
  noRowsFallback?: React.ReactNode;
}

export const DataGrid = <T extends Record<string, any>>({
  columns,
  rows,
  onRowsChange,
  onRowClick,
  onRowDoubleClick,
  selectedRows,
  onSelectedRowsChange,
  rowKeyGetter = (row: T) => row.id || String(row.key),
  sortColumns = [],
  onSortColumnsChange,
  defaultColumnOptions = {},
  className = '',
  style = {},
  headerRowHeight = 35,
  rowHeight = 35,
  enableVirtualization = true,
  enableFiltering = false,
  enableSorting = true,
  enableSelection = false,
  loading = false,
  emptyRowsRenderer,
  noRowsFallback
}: DataGridProps<T>) => {
  const { getThemeClass, getCSSVariable } = useThemeStyles();
  const [filters, setFilters] = useState<Record<string, string>>({});

  // Convert our column format to react-data-grid format
  const gridColumns = useMemo(() => {
    const convertedColumns: Column<T>[] = [];

    // Add selection column if enabled
    if (enableSelection) {
      convertedColumns.push(SelectColumn);
    }

    // Convert custom columns
    const dataColumns: Column<T>[] = columns.map(col => ({
      key: col.key,
      name: col.name,
      width: col.width,
      minWidth: col.minWidth || 80,
      maxWidth: col.maxWidth,
      resizable: col.resizable !== false,
      sortable: enableSorting && col.sortable !== false,
      editor: col.editable ? (col.editor || textEditor) : undefined,
      formatter: col.formatter || col.renderCell ? (props) => {
        if (col.formatter) {
          return col.formatter({ row: props.row, column: col });
        }
        if (col.renderCell) {
          return col.renderCell({ row: props.row, column: col });
        }
        return props.row[col.key];
      } : undefined,
      headerCellClass: col.headerCellClass,
      cellClass: typeof col.cellClass === 'function' 
        ? (row: T) => col.cellClass!(row)
        : col.cellClass,
      ...defaultColumnOptions
    }));

    return [...convertedColumns, ...dataColumns];
  }, [columns, enableSelection, enableSorting, defaultColumnOptions]);

  // Filter rows based on current filters
  const filteredRows = useMemo(() => {
    if (!enableFiltering || Object.keys(filters).length === 0) {
      return rows;
    }

    return rows.filter(row => {
      return Object.entries(filters).every(([key, filterValue]) => {
        if (!filterValue) return true;
        const cellValue = String(row[key] || '').toLowerCase();
        return cellValue.includes(filterValue.toLowerCase());
      });
    });
  }, [rows, filters, enableFiltering]);

  // Handle row click
  const handleRowClick = useCallback((row: T, column: Column<T>) => {
    const rowIndex = filteredRows.findIndex(r => rowKeyGetter(r) === rowKeyGetter(row));
    onRowClick?.(row, rowIndex);
  }, [filteredRows, rowKeyGetter, onRowClick]);

  // Handle row double click
  const handleRowDoubleClick = useCallback((row: T, column: Column<T>) => {
    const rowIndex = filteredRows.findIndex(r => rowKeyGetter(r) === rowKeyGetter(row));
    onRowDoubleClick?.(row, rowIndex);
  }, [filteredRows, rowKeyGetter, onRowDoubleClick]);

  // Handle filter change
  const handleFilterChange = useCallback((columnKey: string, value: string) => {
    setFilters(prev => ({
      ...prev,
      [columnKey]: value
    }));
  }, []);

  // Render filter row if filtering is enabled
  const renderFilterRow = useCallback(() => {
    if (!enableFiltering) return null;

    return (
      <div className={getThemeClass('data-grid__filter-row')}>
        {enableSelection && <div className="data-grid__filter-cell"></div>}
        {columns.map(col => (
          <div key={col.key} className="data-grid__filter-cell">
            {col.filterable !== false && (
              <input
                type="text"
                placeholder={`Filter ${col.name}...`}
                value={filters[col.key] || ''}
                onChange={(e) => handleFilterChange(col.key, e.target.value)}
                className={getThemeClass('data-grid__filter-input')}
              />
            )}
          </div>
        ))}
      </div>
    );
  }, [enableFiltering, enableSelection, columns, filters, getThemeClass, handleFilterChange]);

  // Render empty state
  const renderEmptyState = useCallback(() => {
    if (noRowsFallback) {
      return noRowsFallback;
    }

    return (
      <div className={getThemeClass('data-grid__empty-state')}>
        <p>No data available</p>
      </div>
    );
  }, [noRowsFallback, getThemeClass]);

  const gridClassName = getThemeClass(`data-grid ${className}`);

  if (loading) {
    return (
      <div className={getThemeClass('data-grid__loading')} style={style}>
        <div className="data-grid__loading-content">
          <div className="data-grid__loading-spinner"></div>
          <p>Loading data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className={gridClassName} style={style}>
      {renderFilterRow()}
      <div className="data-grid__container">
        <DataGridComponent
          columns={gridColumns}
          rows={filteredRows}
          onRowsChange={onRowsChange}
          onRowClick={handleRowClick}
          onRowDoubleClick={handleRowDoubleClick}
          selectedRows={selectedRows}
          onSelectedRowsChange={onSelectedRowsChange}
          rowKeyGetter={rowKeyGetter}
          sortColumns={sortColumns}
          onSortColumnsChange={onSortColumnsChange}
          headerRowHeight={headerRowHeight}
          rowHeight={rowHeight}
          className="data-grid__inner"
          renderers={{
            noRowsFallback: emptyRowsRenderer || renderEmptyState
          }}
        />
      </div>
    </div>
  );
};

export default DataGrid;
