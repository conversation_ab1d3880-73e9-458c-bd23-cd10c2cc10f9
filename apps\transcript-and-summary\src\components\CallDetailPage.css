/**
 * Call Detail Page Styles
 * 
 * Theme-aware styles for the call detail/view page
 */

.call-detail-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: var(--theme-bg-primary, #ffffff);
  color: var(--theme-text-primary, #323130);
  font-family: var(--theme-font-family, 'Segoe UI', sans-serif);
}

/* Header */
.call-detail-header {
  background: var(--theme-bg-secondary, #f8f9fa);
  border-bottom: 1px solid var(--theme-border-primary, #d1d1d1);
  padding: 20px;
}

.header-navigation {
  margin-bottom: 16px;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-info {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.header-row {
  display: flex;
  flex-wrap: wrap;
  gap: 24px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
  min-width: 150px;
}

.info-label {
  font-size: 12px;
  font-weight: 600;
  color: var(--theme-text-secondary, #605e5c);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.info-value {
  font-size: 14px;
  font-weight: 500;
  color: var(--theme-text-primary, #323130);
}

.call-direction {
  padding: 4px 8px;
  border-radius: var(--theme-border-radius-sm, 2px);
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  display: inline-block;
  width: fit-content;
}

.call-direction--inbound {
  background: var(--theme-success-bg, #dff6dd);
  color: var(--theme-success-text, #107c10);
  border: 1px solid var(--theme-success-border, #92c5f7);
}

.call-direction--outbound {
  background: var(--theme-info-bg, #deecf9);
  color: var(--theme-info-text, #0078d4);
  border: 1px solid var(--theme-info-border, #92c5f7);
}

/* Three-column body layout */
.call-detail-body {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 1px;
  flex: 1;
  min-height: 0;
  background: var(--theme-border-primary, #d1d1d1);
}

/* Column styles */
.transcript-column,
.summary-column,
.notes-column {
  display: flex;
  flex-direction: column;
  background: var(--theme-bg-primary, #ffffff);
  min-height: 0;
}

.column-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: var(--theme-bg-secondary, #f8f9fa);
  border-bottom: 1px solid var(--theme-border-primary, #d1d1d1);
  gap: 16px;
}

.column-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--theme-text-primary, #323130);
}

.column-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.search-input {
  padding: 6px 10px;
  border: 1px solid var(--theme-border-primary, #d1d1d1);
  border-radius: var(--theme-border-radius-sm, 2px);
  background: var(--theme-bg-primary, #ffffff);
  color: var(--theme-text-primary, #323130);
  font-size: 12px;
  font-family: inherit;
  width: 140px;
}

.search-input:focus {
  outline: none;
  border-color: var(--theme-primary, #0078d4);
  box-shadow: 0 0 0 1px var(--theme-primary, #0078d4);
}

.search-input::placeholder {
  color: var(--theme-text-secondary, #605e5c);
}

.copy-button {
  min-width: 60px;
}

.saving-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
  color: var(--theme-text-secondary, #605e5c);
  font-size: 12px;
}

/* Content areas */
.transcript-content,
.summary-content,
.notes-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  min-height: 0;
}

/* Transcript styles */
.transcript-text {
  line-height: 1.6;
  white-space: pre-wrap;
  font-size: 14px;
  color: var(--theme-text-primary, #323130);
}

/* Summary styles */
.summary-section {
  margin-bottom: 24px;
}

.summary-section:last-child {
  margin-bottom: 0;
}

.summary-section h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
  color: var(--theme-primary, #0078d4);
}

.summary-section p {
  margin: 0;
  line-height: 1.5;
  font-size: 14px;
  color: var(--theme-text-primary, #323130);
}

.key-points-list {
  margin: 0 0 12px 0;
  padding-left: 20px;
}

.key-points-list li {
  margin-bottom: 6px;
  line-height: 1.4;
  font-size: 14px;
  color: var(--theme-text-primary, #323130);
}

.summary-text {
  line-height: 1.5;
  font-size: 14px;
  color: var(--theme-text-primary, #323130);
}

/* Notes editor styles */
.notes-editor {
  width: 100%;
  height: calc(100% - 40px);
  border: 1px solid var(--theme-border-primary, #d1d1d1);
  border-radius: var(--theme-border-radius, 4px);
  padding: 12px;
  font-size: 14px;
  font-family: inherit;
  line-height: 1.5;
  background: var(--theme-bg-primary, #ffffff);
  color: var(--theme-text-primary, #323130);
  resize: none;
}

.notes-editor:focus {
  outline: none;
  border-color: var(--theme-primary, #0078d4);
  box-shadow: 0 0 0 1px var(--theme-primary, #0078d4);
}

.notes-editor::placeholder {
  color: var(--theme-text-secondary, #605e5c);
  font-style: italic;
}

.notes-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
  font-size: 12px;
  color: var(--theme-text-secondary, #605e5c);
}

.character-count {
  font-weight: 500;
}

.auto-save-info {
  font-style: italic;
}

/* Search highlighting */
.search-highlight {
  background: var(--theme-highlight-bg, #ffeb3b);
  color: var(--theme-highlight-text, #000000);
  padding: 1px 2px;
  border-radius: 2px;
}

/* Theme-specific overrides */

/* CRM Theme */
[data-theme="crm"] .call-detail-page {
  --theme-success-bg: #dff6dd;
  --theme-success-text: #107c10;
  --theme-success-border: #92c5f7;
  --theme-info-bg: #deecf9;
  --theme-info-text: #0078d4;
  --theme-info-border: #92c5f7;
  --theme-highlight-bg: #fff4ce;
  --theme-highlight-text: #323130;
}

/* MFE Theme */
[data-theme="mfe"] .call-detail-page {
  --theme-success-bg: #dcfce7;
  --theme-success-text: #166534;
  --theme-success-border: #bbf7d0;
  --theme-info-bg: #dbeafe;
  --theme-info-text: #1d4ed8;
  --theme-info-border: #93c5fd;
  --theme-highlight-bg: #fef3c7;
  --theme-highlight-text: #1f2937;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .call-detail-body {
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 1fr 1fr;
  }
  
  .notes-column {
    grid-column: 1 / -1;
  }
}

@media (max-width: 768px) {
  .call-detail-page {
    height: auto;
    min-height: 100vh;
  }
  
  .call-detail-header {
    padding: 16px;
  }
  
  .header-row {
    gap: 16px;
  }
  
  .info-item {
    min-width: 120px;
  }
  
  .call-detail-body {
    grid-template-columns: 1fr;
    grid-template-rows: auto auto auto;
    min-height: 60vh;
  }
  
  .transcript-content,
  .summary-content,
  .notes-content {
    padding: 16px;
    min-height: 300px;
  }
  
  .column-header {
    padding: 12px 16px;
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
  
  .column-actions {
    justify-content: space-between;
  }
  
  .search-input {
    flex: 1;
    width: auto;
  }
}

@media (max-width: 480px) {
  .call-detail-header {
    padding: 12px;
  }
  
  .header-row {
    flex-direction: column;
    gap: 12px;
  }
  
  .info-item {
    min-width: 0;
  }
  
  .column-header h3 {
    font-size: 14px;
  }
  
  .search-input {
    font-size: 14px; /* Prevent zoom on iOS */
  }
  
  .notes-editor {
    font-size: 14px; /* Prevent zoom on iOS */
  }
}
