import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { DataGrid, DataGridColumn, Button, LoadingSpinner } from '@shared/components';
import { useThemeStyles } from '@shared/services/theme';
import { SortColumn } from 'react-data-grid';
import { CallRecord, MockCallRecordsAPI } from '../services/mockDataService';
import { logger } from '@shared/utils';
import './CallLogPage.css';

interface CallLogPageProps {
  onViewCall: (callRecord: CallRecord) => void;
}

export const CallLogPage: React.FC<CallLogPageProps> = ({ onViewCall }) => {
  const { getThemeClass } = useThemeStyles();
  const [callRecords, setCallRecords] = useState<CallRecord[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalRecords, setTotalRecords] = useState(0);
  const [pageSize] = useState(20);
  const [sortColumns, setSortColumns] = useState<SortColumn[]>([
    { columnKey: 'dateOfCall', direction: 'DESC' }
  ]);
  const [searchQuery, setSearchQuery] = useState('');
  const [savingNotes, setSavingNotes] = useState<Set<string>>(new Set());

  const apiService = MockCallRecordsAPI.getInstance();

  // Load call records
  const loadCallRecords = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await apiService.getCallRecords(currentPage, pageSize);
      setCallRecords(response.records);
      setTotalPages(response.totalPages);
      setTotalRecords(response.total);
      logger.info('Call records loaded', { 
        page: currentPage, 
        count: response.records.length,
        total: response.total 
      });
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load call records';
      setError(errorMessage);
      logger.error('Failed to load call records', err);
    } finally {
      setLoading(false);
    }
  }, [apiService, currentPage, pageSize]);

  // Load data on mount and page change
  useEffect(() => {
    loadCallRecords();
  }, [loadCallRecords]);

  // Handle notes auto-save
  const handleNotesChange = useCallback(async (callId: string, notes: string) => {
    setSavingNotes(prev => new Set(prev).add(callId));
    
    try {
      await apiService.updateCallRecord(callId, { notes });
      
      // Update local state
      setCallRecords(prev => prev.map(record => 
        record.id === callId ? { ...record, notes } : record
      ));
      
      logger.info('Notes saved', { callId, notesLength: notes.length });
    } catch (err) {
      logger.error('Failed to save notes', { callId, error: err });
    } finally {
      setSavingNotes(prev => {
        const newSet = new Set(prev);
        newSet.delete(callId);
        return newSet;
      });
    }
  }, [apiService]);

  // Debounced notes save
  const debouncedNotesChange = useMemo(() => {
    const timeouts = new Map<string, NodeJS.Timeout>();
    
    return (callId: string, notes: string) => {
      // Clear existing timeout for this call
      const existingTimeout = timeouts.get(callId);
      if (existingTimeout) {
        clearTimeout(existingTimeout);
      }
      
      // Set new timeout
      const timeout = setTimeout(() => {
        handleNotesChange(callId, notes);
        timeouts.delete(callId);
      }, 1000); // 1 second delay
      
      timeouts.set(callId, timeout);
    };
  }, [handleNotesChange]);

  // Handle row click
  const handleRowClick = useCallback((row: CallRecord) => {
    onViewCall(row);
    logger.info('Call record selected for viewing', { callId: row.id });
  }, [onViewCall]);

  // Handle pagination
  const handlePageChange = useCallback((page: number) => {
    setCurrentPage(page);
  }, []);

  // Handle search
  const handleSearch = useCallback(async () => {
    if (!searchQuery.trim()) {
      loadCallRecords();
      return;
    }

    setLoading(true);
    try {
      const results = await apiService.searchCallRecords(searchQuery);
      setCallRecords(results);
      setTotalPages(1);
      setTotalRecords(results.length);
      setCurrentPage(1);
      logger.info('Search completed', { query: searchQuery, results: results.length });
    } catch (err) {
      logger.error('Search failed', err);
      setError('Search failed');
    } finally {
      setLoading(false);
    }
  }, [searchQuery, apiService, loadCallRecords]);

  // Clear search
  const handleClearSearch = useCallback(() => {
    setSearchQuery('');
    setCurrentPage(1);
    loadCallRecords();
  }, [loadCallRecords]);

  // Define grid columns
  const columns: DataGridColumn<CallRecord>[] = useMemo(() => [
    {
      key: 'dateOfCall',
      name: 'Date of Call',
      width: 120,
      sortable: true,
      formatter: ({ row }) => row.dateOfCall
    },
    {
      key: 'timeOfCall',
      name: 'Time of Call',
      width: 100,
      sortable: true,
      formatter: ({ row }) => row.timeOfCall
    },
    {
      key: 'callLength',
      name: 'Call Length',
      width: 100,
      sortable: true,
      formatter: ({ row }) => row.callLength
    },
    {
      key: 'name',
      name: 'Name',
      width: 150,
      sortable: true,
      filterable: true,
      formatter: ({ row }) => row.name
    },
    {
      key: 'inboundOutbound',
      name: 'Inbound/Outbound',
      width: 140,
      sortable: true,
      filterable: true,
      formatter: ({ row }) => (
        <span className={getThemeClass(`call-direction call-direction--${row.inboundOutbound.toLowerCase()}`)}>
          {row.inboundOutbound}
        </span>
      )
    },
    {
      key: 'phoneNumber',
      name: 'Phone Number',
      width: 140,
      sortable: true,
      filterable: true,
      formatter: ({ row }) => row.phoneNumber
    },
    {
      key: 'notes',
      name: 'Notes',
      width: 200,
      editable: true,
      formatter: ({ row }) => (
        <div className="notes-cell">
          <textarea
            value={row.notes}
            onChange={(e) => {
              // Update local state immediately for responsive UI
              setCallRecords(prev => prev.map(record => 
                record.id === row.id ? { ...record, notes: e.target.value } : record
              ));
              // Trigger debounced save
              debouncedNotesChange(row.id, e.target.value);
            }}
            placeholder="Add notes..."
            className={getThemeClass('notes-textarea')}
            maxLength={255}
            rows={2}
          />
          {savingNotes.has(row.id) && (
            <div className="notes-saving-indicator">
              <span>Saving...</span>
            </div>
          )}
        </div>
      )
    },
    {
      key: 'actions',
      name: 'Action',
      width: 100,
      formatter: ({ row }) => (
        <Button
          onClick={(e) => {
            e.stopPropagation();
            handleRowClick(row);
          }}
          variant="primary"
          size="small"
          className={getThemeClass('action-button')}
        >
          View
        </Button>
      )
    }
  ], [getThemeClass, debouncedNotesChange, savingNotes, handleRowClick]);

  // Handle rows change (for editing)
  const handleRowsChange = useCallback((updatedRows: CallRecord[]) => {
    setCallRecords(updatedRows);
  }, []);

  // Render pagination controls
  const renderPaginationControls = () => {
    if (totalPages <= 1) return null;

    const pages = [];
    const maxVisiblePages = 5;
    let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

    if (endPage - startPage + 1 < maxVisiblePages) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    // Previous button
    pages.push(
      <Button
        key="prev"
        onClick={() => handlePageChange(currentPage - 1)}
        disabled={currentPage === 1}
        variant="secondary"
        size="small"
        className={getThemeClass('pagination-button')}
      >
        Prev
      </Button>
    );

    // Page numbers
    for (let i = startPage; i <= endPage; i++) {
      pages.push(
        <Button
          key={i}
          onClick={() => handlePageChange(i)}
          variant={i === currentPage ? "primary" : "secondary"}
          size="small"
          className={getThemeClass('pagination-button')}
        >
          {i}
        </Button>
      );
    }

    // Next button
    pages.push(
      <Button
        key="next"
        onClick={() => handlePageChange(currentPage + 1)}
        disabled={currentPage === totalPages}
        variant="secondary"
        size="small"
        className={getThemeClass('pagination-button')}
      >
        Next
      </Button>
    );

    return (
      <div className={getThemeClass('pagination-controls')}>
        <div className="pagination-info">
          Showing {((currentPage - 1) * pageSize) + 1} to {Math.min(currentPage * pageSize, totalRecords)} of {totalRecords} records
        </div>
        <div className="pagination-buttons">
          {pages}
        </div>
      </div>
    );
  };

  if (error) {
    return (
      <div className={getThemeClass('call-log-page call-log-page--error')}>
        <div className="error-message">
          <h3>Error Loading Call Records</h3>
          <p>{error}</p>
          <Button onClick={loadCallRecords} variant="primary">
            Retry
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className={getThemeClass('call-log-page')}>
      {/* Header */}
      <div className="call-log-header">
        <h1 className="call-log-title">Call Log</h1>
        <div className="call-log-actions">
          <div className="search-container">
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
              placeholder="Search name..."
              className={getThemeClass('search-input')}
            />
            <Button
              onClick={handleSearch}
              variant="secondary"
              size="small"
              disabled={loading}
            >
              Search
            </Button>
            {searchQuery && (
              <Button
                onClick={handleClearSearch}
                variant="secondary"
                size="small"
              >
                Clear
              </Button>
            )}
          </div>
          <Button
            onClick={loadCallRecords}
            variant="secondary"
            size="small"
            disabled={loading}
          >
            Refresh
          </Button>
        </div>
      </div>

      {/* Data Grid */}
      <div className="call-log-content">
        <DataGrid
          columns={columns}
          rows={callRecords}
          onRowsChange={handleRowsChange}
          onRowClick={handleRowClick}
          sortColumns={sortColumns}
          onSortColumnsChange={setSortColumns}
          loading={loading}
          enableFiltering={true}
          enableSorting={true}
          className="call-log-grid"
          rowHeight={60}
          headerRowHeight={40}
          noRowsFallback={
            <div className="no-records-message">
              {searchQuery ? 'No records found matching your search.' : 'No call records available.'}
            </div>
          }
        />
      </div>

      {/* Pagination */}
      {renderPaginationControls()}
    </div>
  );
};

export default CallLogPage;
