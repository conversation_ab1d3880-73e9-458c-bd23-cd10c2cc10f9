import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, FilterRow, DateRangeValue, Pagination } from '@shared/components';
import { useThemeStyles } from '@shared/services/theme';
import DataGridComponent, { Column, SortColumn } from 'react-data-grid';
import 'react-data-grid/lib/styles.css';
import { CallRecord, MockCallRecordsAPI } from '../services/mockDataService';
import { logger } from '@shared/utils';
import './CallLogPage.css';

interface CallLogPageProps {
  onViewCall: (callRecord: CallRecord) => void;
}

export const CallLogPage: React.FC<CallLogPageProps> = ({ onViewCall }) => {
  const { getThemeClass } = useThemeStyles();
  const [callRecords, setCallRecords] = useState<CallRecord[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalRecords, setTotalRecords] = useState(0);
  const [pageSize] = useState(20);
  const [sortColumns, setSortColumns] = useState<SortColumn[]>([
    { columnKey: 'dateOfCall', direction: 'DESC' }
  ]);
  const [savingNotes, setSavingNotes] = useState<Set<string>>(new Set());

  // Column filters state
  const [columnFilters, setColumnFilters] = useState<Record<string, string>>({});
  const [dateRangeFilter, setDateRangeFilter] = useState<DateRangeValue>({ from: '', to: '' });

  const apiService = MockCallRecordsAPI.getInstance();

  // Load call records with filtering
  const loadCallRecords = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await apiService.getCallRecords(currentPage, pageSize);
      let filteredRecords = response.records;

      // Apply column filters
      Object.entries(columnFilters).forEach(([column, filterValue]) => {
        if (filterValue.trim()) {
          filteredRecords = filteredRecords.filter(record => {
            const value = record[column as keyof CallRecord];
            return String(value).toLowerCase().includes(filterValue.toLowerCase());
          });
        }
      });

      // Apply date range filter
      if (dateRangeFilter.from || dateRangeFilter.to) {
        filteredRecords = filteredRecords.filter(record => {
          const recordDate = new Date(record.dateOfCall);
          const fromDate = dateRangeFilter.from ? new Date(dateRangeFilter.from) : null;
          const toDate = dateRangeFilter.to ? new Date(dateRangeFilter.to) : null;

          if (fromDate && recordDate < fromDate) return false;
          if (toDate && recordDate > toDate) return false;
          return true;
        });
      }

      setCallRecords(filteredRecords);
      setTotalPages(Math.ceil(filteredRecords.length / pageSize));
      setTotalRecords(filteredRecords.length);

      logger.info('Call records loaded and filtered', {
        page: currentPage,
        total: filteredRecords.length,
        originalTotal: response.total,
        filters: columnFilters,
        dateRange: dateRangeFilter
      });
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load call records';
      setError(errorMessage);
      logger.error('Failed to load call records', err);
    } finally {
      setLoading(false);
    }
  }, [apiService, currentPage, pageSize, columnFilters, dateRangeFilter]);

  // Load data on mount and page change
  useEffect(() => {
    loadCallRecords();
  }, [loadCallRecords]);

  // Handle notes auto-save
  const handleNotesChange = useCallback(async (callId: string, notes: string) => {
    setSavingNotes(prev => new Set(prev).add(callId));
    
    try {
      await apiService.updateCallRecord(callId, { notes });
      
      // Update local state
      setCallRecords(prev => prev.map(record => 
        record.id === callId ? { ...record, notes } : record
      ));
      
      logger.info('Notes saved', { callId, notesLength: notes.length });
    } catch (err) {
      logger.error('Failed to save notes', { callId, error: err });
    } finally {
      setSavingNotes(prev => {
        const newSet = new Set(prev);
        newSet.delete(callId);
        return newSet;
      });
    }
  }, [apiService]);

  // Debounced notes save
  const debouncedNotesChange = useMemo(() => {
    const timeouts = new Map<string, NodeJS.Timeout>();
    
    return (callId: string, notes: string) => {
      // Clear existing timeout for this call
      const existingTimeout = timeouts.get(callId);
      if (existingTimeout) {
        clearTimeout(existingTimeout);
      }
      
      // Set new timeout
      const timeout = setTimeout(() => {
        handleNotesChange(callId, notes);
        timeouts.delete(callId);
      }, 1000); // 1 second delay
      
      timeouts.set(callId, timeout);
    };
  }, [handleNotesChange]);

  // Handle row click
  const handleRowClick = useCallback((row: CallRecord) => {
    onViewCall(row);
    logger.info('Call record selected for viewing', { callId: row.id });
  }, [onViewCall]);

  // Handle row double click
  const handleRowDoubleClick = useCallback((row: CallRecord) => {
    onViewCall(row);
    logger.info('Call record opened via double-click', { callId: row.id });
  }, [onViewCall]);

  // Handle pagination
  const handlePageChange = useCallback((page: number) => {
    setCurrentPage(page);
  }, []);

  // Handle column filter changes
  const handleColumnFilterChange = useCallback((column: string, value: string) => {
    setColumnFilters(prev => ({
      ...prev,
      [column]: value
    }));
    setCurrentPage(1); // Reset to first page when filtering
  }, []);

  // Handle date range filter changes
  const handleDateRangeChange = useCallback((value: DateRangeValue) => {
    setDateRangeFilter(value);
    setCurrentPage(1); // Reset to first page when filtering
  }, []);

  // Clear all filters
  const handleClearFilters = useCallback(() => {
    setColumnFilters({});
    setDateRangeFilter({ from: '', to: '' });
    setCurrentPage(1);
  }, []);

  // Define grid columns for react-data-grid
  const columns: Column<CallRecord>[] = useMemo(() => [
    {
      key: 'dateOfCall',
      name: 'Date of Call',
      width: 120,
      sortable: true
    },
    {
      key: 'timeOfCall',
      name: 'Time of Call',
      width: 90,
      sortable: true
    },
    {
      key: 'callLength',
      name: 'Call Length',
      width: 90,
      sortable: true
    },
    {
      key: 'name',
      name: 'Name',
      width: 180,
      sortable: true
    },
    {
      key: 'inboundOutbound',
      name: 'Inbound/Outbound',
      width: 130,
      sortable: true,
      renderCell: ({ row }) => (
        <span className={getThemeClass(`call-direction call-direction--${row.inboundOutbound.toLowerCase()}`)}>
          {row.inboundOutbound}
        </span>
      )
    },
    {
      key: 'phoneNumber',
      name: 'Phone Number',
      width: 150,
      sortable: true
    },
    {
      key: 'notes',
      name: 'Notes',
      width: 250,
      renderCell: ({ row }) => (
        <div className="notes-cell">
          <textarea
            value={row.notes}
            onChange={(e) => {
              // Update local state immediately for responsive UI
              setCallRecords(prev => prev.map(record =>
                record.id === row.id ? { ...record, notes: e.target.value } : record
              ));
              // Trigger debounced save
              debouncedNotesChange(row.id, e.target.value);
            }}
            placeholder="Add notes..."
            className={getThemeClass('notes-textarea')}
            maxLength={255}
            rows={2}
          />
          {savingNotes.has(row.id) && (
            <div className="notes-saving-indicator">
              <span>Saving...</span>
            </div>
          )}
        </div>
      )
    },
    {
      key: 'actions',
      name: 'Action',
      width: 80,
      renderCell: ({ row }) => (
        <Button
          onClick={(e) => {
            e.stopPropagation();
            handleRowClick(row);
          }}
          variant="primary"
          size="small"
          className={getThemeClass('action-button')}
        >
          View
        </Button>
      )
    }
  ], [getThemeClass, debouncedNotesChange, savingNotes, handleRowClick]);

  // Define filter column configuration
  const filterColumns = useMemo(() => [
    { key: 'dateOfCall', name: 'Date of Call', width: 120, filterType: 'date-range' as const },
    { key: 'timeOfCall', name: 'Time of Call', width: 90, placeholder: 'Filter time...' },
    { key: 'callLength', name: 'Call Length', width: 90, placeholder: 'Filter length...' },
    { key: 'name', name: 'Name', width: 180, placeholder: 'Filter name...' },
    { key: 'inboundOutbound', name: 'Inbound/Outbound', width: 130, placeholder: 'Filter direction...' },
    { key: 'phoneNumber', name: 'Phone Number', width: 150, placeholder: 'Filter phone...' },
    { key: 'notes', name: 'Notes', width: 250, placeholder: 'Filter notes...' },
    { key: 'actions', name: 'Action', width: 80 }
  ], []);

  // Handle rows change (for editing)
  const handleRowsChange = useCallback((updatedRows: CallRecord[]) => {
    setCallRecords(updatedRows);
  }, []);



  if (error) {
    return (
      <div className={getThemeClass('call-log-page call-log-page--error')}>
        <div className="error-message">
          <h3>Error Loading Call Records</h3>
          <p>{error}</p>
          <Button onClick={loadCallRecords} variant="primary">
            Retry
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className={getThemeClass('call-log-page')}>
      {/* Header */}
      <div className="call-log-header">
        <h1 className={getThemeClass('call-log-title')}>Call Log</h1>
        <div className="call-log-actions">
          <Button
            onClick={handleClearFilters}
            variant="secondary"
            size="small"
            className="clear-filters-button"
            disabled={loading}
          >
            Clear All Filters
          </Button>
          <Button
            onClick={loadCallRecords}
            variant="secondary"
            size="small"
            disabled={loading}
          >
            Refresh
          </Button>
        </div>
      </div>

      {/* Filter Row */}
      <FilterRow
        columnFilters={columnFilters}
        dateRangeFilter={dateRangeFilter}
        onColumnFilterChange={handleColumnFilterChange}
        onDateRangeChange={handleDateRangeChange}
        onClearFilters={handleClearFilters}
        columns={filterColumns}
      />

      {/* Data Grid */}
      <div className="call-log-content">
        {loading ? (
          <div className="loading-container">
            <LoadingSpinner message="Loading data..." />
          </div>
        ) : (
          <div
            className="data-grid-wrapper"
            onDoubleClick={(e) => {
              // Find the row that was double-clicked
              const target = e.target as HTMLElement;
              const rowElement = target.closest('[role="row"]');
              if (rowElement && rowElement.getAttribute('aria-rowindex')) {
                const rowIndex = parseInt(rowElement.getAttribute('aria-rowindex')!) - 2; // -2 because header is index 1
                if (rowIndex >= 0 && rowIndex < callRecords.length) {
                  handleRowDoubleClick(callRecords[rowIndex]);
                }
              }
            }}
          >
            <DataGridComponent
              columns={columns}
              rows={callRecords}
              onRowsChange={handleRowsChange}
              sortColumns={sortColumns}
              onSortColumnsChange={setSortColumns}
              className="call-log-grid"
              rowHeight={60}
              headerRowHeight={40}
              renderers={{
                noRowsFallback: (
                  <div className="no-records-message">
                    No call records available.
                  </div>
                )
              }}
            />
          </div>
        )}
      </div>

      {/* Pagination */}
      <Pagination
        currentPage={currentPage}
        totalPages={totalPages}
        totalRecords={totalRecords}
        pageSize={pageSize}
        onPageChange={handlePageChange}
        showPageInfo={true}
        showPageNumbers={true}
        maxPageNumbers={5}
      />
    </div>
  );
};

export default CallLogPage;
