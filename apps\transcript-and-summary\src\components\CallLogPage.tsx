import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, GridColumn } from '@shared/components';
import { SimpleGrid } from '@shared/components/Grid/SimpleGrid';
import { useThemeStyles } from '@shared/services/theme';
import { CallRecord, MockCallRecordsAPI } from '../services/mockDataService';
import { logger } from '@shared/utils';
import './CallLogPage.css';

// Define types for our grid (currently unused but ready for future features)



interface CallLogPageProps {
  onViewCall: (callRecord: CallRecord) => void;
}

export const CallLogPage: React.FC<CallLogPageProps> = ({ onViewCall }) => {
  const { getThemeClass } = useThemeStyles();
  const [callRecords, setCallRecords] = useState<CallRecord[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage] = useState(1);

  const [pageSize] = useState(10);
  const [savingNotes, setSavingNotes] = useState<Set<string>>(new Set());



  const apiService = MockCallRecordsAPI.getInstance();

  // Load call records with filtering
  const loadCallRecords = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await apiService.getCallRecords(currentPage, pageSize);
      setCallRecords(response.records);

      logger.info('Call records loaded', {
        page: currentPage,
        total: response.records.length
      });
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load call records';
      setError(errorMessage);
      logger.error('Failed to load call records', err);
    } finally {
      setLoading(false);
    }
  }, [apiService, currentPage, pageSize]);

  // Load data on mount and page change
  useEffect(() => {
    loadCallRecords();
  }, [loadCallRecords]);

  // Handle notes auto-save
  const handleNotesChange = useCallback(async (callId: string, notes: string) => {
    setSavingNotes(prev => new Set(prev).add(callId));
    
    try {
      await apiService.updateCallRecord(callId, { notes });
      
      // Update local state
      setCallRecords(prev => prev.map(record => 
        record.id === callId ? { ...record, notes } : record
      ));
      
      logger.info('Notes saved', { callId, notesLength: notes.length });
    } catch (err) {
      logger.error('Failed to save notes', { callId, error: err });
    } finally {
      setSavingNotes(prev => {
        const newSet = new Set(prev);
        newSet.delete(callId);
        return newSet;
      });
    }
  }, [apiService]);

  // Debounced notes save
  const debouncedNotesChange = useMemo(() => {
    const timeouts = new Map<string, NodeJS.Timeout>();
    
    return (callId: string, notes: string) => {
      // Clear existing timeout for this call
      const existingTimeout = timeouts.get(callId);
      if (existingTimeout) {
        clearTimeout(existingTimeout);
      }
      
      // Set new timeout
      const timeout = setTimeout(() => {
        handleNotesChange(callId, notes);
        timeouts.delete(callId);
      }, 1000); // 1 second delay
      
      timeouts.set(callId, timeout);
    };
  }, [handleNotesChange]);

  // Handle row click
  const handleRowClick = useCallback((row: CallRecord) => {
    onViewCall(row);
    logger.info('Call record selected for viewing', { callId: row.id });
  }, [onViewCall]);

  // Handle row double click
  const handleRowDoubleClick = useCallback((row: CallRecord) => {
    onViewCall(row);
    logger.info('Call record opened via double-click', { callId: row.id });
  }, [onViewCall]);



  // Define grid columns for our custom Grid
  const columns: GridColumn<CallRecord>[] = useMemo(() => [
    {
      key: 'dateOfCall',
      title: 'Date of Call',
      width: 170,
      sortable: true,
      filterable: true,
      type: 'date'
    },
    {
      key: 'timeOfCall',
      title: 'Time of Call',
      width: 150,
      sortable: true,
      filterable: true,
      type: 'text'
    },
    {
      key: 'callLength',
      title: 'Call Length',
      width: 150,
      sortable: true,
      filterable: true,
      type: 'text'
    },
    {
      key: 'name',
      title: 'Name',
      width: 180,
      sortable: true,
      filterable: true,
      type: 'text'
    },
    {
      key: 'inboundOutbound',
      title: 'Inbound/Outbound',
      width: 210,
      sortable: true,
      filterable: true,
      type: 'select',
      filterOptions: [
        { value: 'Inbound', label: 'Inbound' },
        { value: 'Outbound', label: 'Outbound' }
      ],
      renderer: ({ value }) => (
        <span className={getThemeClass(`call-direction call-direction--${value.toLowerCase()}`)}>
          {value}
        </span>
      )
    },
    {
      key: 'phoneNumber',
      title: 'Phone Number',
      width: 150,
      sortable: true,
      filterable: true,
      type: 'text'
    },
    {
      key: 'notes',
      title: 'Notes',
      width: 250,
      editable: true,
      type: 'text',
      renderer: ({ value, row }) => (
        <div className="notes-cell">
          <textarea
            value={value || ''}
            onChange={(e) => {
              // Update local state immediately for responsive UI
              setCallRecords(prev => prev.map(record =>
                record.id === row.id ? { ...record, notes: e.target.value } : record
              ));
              // Trigger debounced save
              debouncedNotesChange(row.id, e.target.value);
            }}
            placeholder="Add notes..."
            className={getThemeClass('notes-textarea')}
            maxLength={255}
            rows={2}
          />
          {savingNotes.has(row.id) && (
            <div className="notes-saving-indicator">
              <span>Saving...</span>
            </div>
          )}
        </div>
      )
    },
    {
      key: 'actions',
      title: 'Action',
      width: 80,
      sortable: false,
      filterable: false,
      renderer: ({ row }) => (
        <Button
          onClick={(e) => {
            e.stopPropagation();
            handleRowClick(row);
          }}
          variant="primary"
          size="small"
          className={getThemeClass('action-button')}
        >
          View
        </Button>
      )
    }
  ], [getThemeClass, debouncedNotesChange, savingNotes, handleRowClick]);





  if (error) {
    return (
      <div className={getThemeClass('call-log-page call-log-page--error')}>
        <div className="error-message">
          <h3>Error Loading Call Records</h3>
          <p>{error}</p>
          <Button onClick={loadCallRecords} variant="primary">
            Retry
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className={getThemeClass('call-log-page')}>
      {/* Header */}
      <div className="call-log-header">
        <h1 className={getThemeClass('call-log-title')}>Call Log</h1>
        <div className="call-log-actions">

          <Button
            onClick={loadCallRecords}
            variant="secondary"
            size="small"
            disabled={loading}
          >
            Refresh
          </Button>
        </div>
      </div>

      {/* Custom Grid */}
      <div className="call-log-content">
        <SimpleGrid
          data={callRecords}
          columns={columns}
          theme="crm"
          loading={loading}
          height="100%"
          onRowDoubleClick={(row) => {
            handleRowDoubleClick(row);
          }}
          className="call-log-grid"
          emptyComponent={
            <div className="no-records-message">
              No call records available.
            </div>
          }
          loadingComponent={
            <LoadingSpinner message="Loading data..." />
          }
        />
      </div>
    </div>
  );
};

export default CallLogPage;
