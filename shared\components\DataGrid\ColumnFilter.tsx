import React, { useState, useCallback } from 'react';
import { useThemeStyles } from '../../services/theme';
import './ColumnFilter.css';

export interface ColumnFilterProps {
  column: string;
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  type?: 'text' | 'date-range';
}

export interface DateRangeValue {
  from: string;
  to: string;
}

export interface DateRangeFilterProps {
  column: string;
  value: DateRangeValue;
  onChange: (value: DateRangeValue) => void;
}

export const ColumnFilter: React.FC<ColumnFilterProps> = ({
  column,
  value,
  onChange,
  placeholder = 'Filter...',
  type = 'text'
}) => {
  const { getThemeClass } = useThemeStyles();

  const handleChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    onChange(e.target.value);
  }, [onChange]);

  if (type === 'text') {
    return (
      <input
        type="text"
        value={value}
        onChange={handleChange}
        placeholder={placeholder}
        className={getThemeClass('column-filter-input')}
        onClick={(e) => e.stopPropagation()}
      />
    );
  }

  return null;
};

export const DateRangeFilter: React.FC<DateRangeFilterProps> = ({
  column,
  value,
  onChange
}) => {
  const { getThemeClass } = useThemeStyles();

  const handleFromChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    onChange({ ...value, from: e.target.value });
  }, [value, onChange]);

  const handleToChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    onChange({ ...value, to: e.target.value });
  }, [value, onChange]);

  return (
    <div className={getThemeClass('date-range-filter')} onClick={(e) => e.stopPropagation()}>
      <div className="date-range-input-group">
        <label className="date-range-label">From:</label>
        <input
          type="date"
          value={value.from}
          onChange={handleFromChange}
          className={getThemeClass('date-range-input')}
        />
      </div>
      <div className="date-range-input-group">
        <label className="date-range-label">To:</label>
        <input
          type="date"
          value={value.to}
          onChange={handleToChange}
          className={getThemeClass('date-range-input')}
        />
      </div>
    </div>
  );
};
