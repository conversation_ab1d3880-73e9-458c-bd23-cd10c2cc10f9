import React, { useState, useCallback, useEffect } from 'react';
import { <PERSON><PERSON>, LoadingSpinner } from '@shared/components';
import { useThemeStyles } from '@shared/services/theme';
import { CallRecord, MockCallRecordsAPI } from '../services/mockDataService';
import { logger } from '@shared/utils';
import './CallDetailPage.css';

interface CallDetailPageProps {
  callRecord: CallRecord;
  onBack: () => void;
}

export const CallDetailPage: React.FC<CallDetailPageProps> = ({ 
  callRecord: initialCallRecord, 
  onBack 
}) => {
  const { getThemeClass } = useThemeStyles();
  const [callRecord, setCallRecord] = useState<CallRecord>(initialCallRecord);
  const [userNotes, setUserNotes] = useState(callRecord.notes || '');
  const [savingNotes, setSavingNotes] = useState(false);
  const [copySuccess, setCopySuccess] = useState<'transcript' | 'summary' | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [summarySearchTerm, setSummarySearchTerm] = useState('');

  const apiService = MockCallRecordsAPI.getInstance();

  // Auto-save notes with debouncing
  useEffect(() => {
    if (userNotes === callRecord.notes) return;

    const timeoutId = setTimeout(async () => {
      setSavingNotes(true);
      try {
        const updatedRecord = await apiService.updateCallRecord(callRecord.id, { 
          notes: userNotes 
        });
        if (updatedRecord) {
          setCallRecord(updatedRecord);
          logger.info('User notes saved', { callId: callRecord.id });
        }
      } catch (error) {
        logger.error('Failed to save user notes', error);
      } finally {
        setSavingNotes(false);
      }
    }, 1000);

    return () => clearTimeout(timeoutId);
  }, [userNotes, callRecord.notes, callRecord.id, apiService]);

  // Copy to clipboard functionality
  const copyToClipboard = useCallback(async (text: string, type: 'transcript' | 'summary') => {
    try {
      await navigator.clipboard.writeText(text);
      setCopySuccess(type);
      setTimeout(() => setCopySuccess(null), 2000);
      logger.info('Content copied to clipboard', { type });
    } catch (error) {
      logger.error('Failed to copy to clipboard', error);
    }
  }, []);

  // Highlight search terms in text
  const highlightText = useCallback((text: string, searchTerm: string) => {
    if (!searchTerm.trim()) return text;

    const regex = new RegExp(`(${searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
    return text.replace(regex, '<mark class="search-highlight">$1</mark>');
  }, []);

  // Format call duration for display
  const formatCallDuration = (duration: string) => {
    const [minutes, seconds] = duration.split(':');
    return `${minutes}m ${seconds}s`;
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-GB', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    });
  };

  return (
    <div className={getThemeClass('call-detail-page')}>
      {/* Header */}
      <div className="call-detail-header">
        <div className="header-navigation">
          <Button
            onClick={onBack}
            variant="secondary"
            size="small"
            className="back-button"
          >
            ← Back
          </Button>
        </div>
        
        <div className="header-info">
          <div className="header-row">
            <div className="info-item">
              <span className="info-label">Date of Call:</span>
              <span className="info-value">{formatDate(callRecord.dateOfCall)}</span>
            </div>
            <div className="info-item">
              <span className="info-label">Time of Call:</span>
              <span className="info-value">{callRecord.timeOfCall}</span>
            </div>
            <div className="info-item">
              <span className="info-label">Call Length:</span>
              <span className="info-value">{formatCallDuration(callRecord.callLength)}</span>
            </div>
            <div className="info-item">
              <span className="info-label">Call ID:</span>
              <span className="info-value">{callRecord.callId}</span>
            </div>
            <div className="info-item">
              <span className="info-label">Call Type:</span>
              <span className="info-value">{callRecord.callType}</span>
            </div>
            <div className="info-item">
              <span className="info-label">User Name:</span>
              <span className="info-value">{callRecord.userName}</span>
            </div>
          </div>
          <div className="header-row">
            <div className="info-item">
              <span className="info-label">Call Direction:</span>
              <span className={getThemeClass(`info-value call-direction call-direction--${callRecord.callDirection.toLowerCase()}`)}>
                {callRecord.callDirection}
              </span>
            </div>
            <div className="info-item">
              <span className="info-label">Phone Number:</span>
              <span className="info-value">{callRecord.phoneNumber}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Three-column body layout */}
      <div className="call-detail-body">
        {/* Column 1: Call Transcript */}
        <div className="transcript-column">
          <div className="column-header">
            <h3>Call Transcript</h3>
            <div className="column-actions">
              <input
                type="text"
                placeholder="Search transcript..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className={getThemeClass('search-input')}
              />
              <Button
                onClick={() => copyToClipboard(callRecord.transcript, 'transcript')}
                variant="secondary"
                size="small"
                className="copy-button"
              >
                {copySuccess === 'transcript' ? 'Copied!' : 'Copy'}
              </Button>
            </div>
          </div>
          <div className="transcript-content">
            <div 
              className="transcript-text"
              dangerouslySetInnerHTML={{
                __html: highlightText(callRecord.transcript, searchTerm)
              }}
            />
          </div>
        </div>

        {/* Column 2: AI-generated Summary */}
        <div className="summary-column">
          <div className="column-header">
            <h3>Call Summary</h3>
            <div className="column-actions">
              <input
                type="text"
                placeholder="Search summary..."
                value={summarySearchTerm}
                onChange={(e) => setSummarySearchTerm(e.target.value)}
                className={getThemeClass('search-input')}
              />
              <Button
                onClick={() => copyToClipboard(callRecord.summary, 'summary')}
                variant="secondary"
                size="small"
                className="copy-button"
              >
                {copySuccess === 'summary' ? 'Copied!' : 'Copy'}
              </Button>
            </div>
          </div>
          <div className="summary-content">
            <div className="summary-section">
              <h4>Call Purpose:</h4>
              <p>Customer inquiry regarding account closure status</p>
            </div>
            <div className="summary-section">
              <h4>Key Points:</h4>
              <ul className="key-points-list">
                <li>Customer submitted account closure request one week ago</li>
                <li>Account number: ACC123456789</li>
                <li>Customer expressed urgency for resolution</li>
                <li>Agent confirmed request is in progress</li>
              </ul>
              <div
                className="summary-text"
                dangerouslySetInnerHTML={{
                  __html: highlightText(callRecord.summary, summarySearchTerm)
                }}
              />
            </div>
            <div className="summary-section">
              <h4>Resolution:</h4>
              <p>Agent provided timeline of 2-3 business days for completion with email confirmation to follow</p>
            </div>
          </div>
        </div>

        {/* Column 3: User Notes Editor */}
        <div className="notes-column">
          <div className="column-header">
            <h3>Contact Notes Editor</h3>
            <div className="column-actions">
              <Button
                onClick={() => copyToClipboard(userNotes, 'transcript')}
                variant="secondary"
                size="small"
                className="copy-button"
              >
                Copy
              </Button>
              {savingNotes && (
                <div className="saving-indicator">
                  <LoadingSpinner size="small" />
                  <span>Saving...</span>
                </div>
              )}
            </div>
          </div>
          <div className="notes-content">
            <textarea
              value={userNotes}
              onChange={(e) => setUserNotes(e.target.value)}
              placeholder="Enter your notes here..."
              className={getThemeClass('notes-editor')}
            />
            <div className="notes-info">
              <span className="character-count">
                {userNotes.length} characters
              </span>
              <span className="auto-save-info">
                Auto-saves after 1 second of inactivity
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CallDetailPage;
