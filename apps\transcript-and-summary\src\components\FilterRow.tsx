import React from 'react';
import { useThemeStyles } from '@shared/services/theme';
import { ColumnFilter, DateRangeFilter, DateRangeValue } from './ColumnFilter';
import './FilterRow.css';

export interface FilterRowProps {
  columnFilters: Record<string, string>;
  dateRangeFilter: DateRangeValue;
  onColumnFilterChange: (column: string, value: string) => void;
  onDateRangeChange: (value: DateRangeValue) => void;
  onClearFilters: () => void;
}

export const FilterRow: React.FC<FilterRowProps> = ({
  columnFilters,
  dateRangeFilter,
  onColumnFilterChange,
  onDateRangeChange,
  onClearFilters
}) => {
  const { getThemeClass } = useThemeStyles();

  return (
    <div className={getThemeClass('filter-row')}>
      <div className="filter-row-content">
        {/* Date of Call Filter */}
        <div className="filter-cell" style={{ width: '140px' }}>
          <DateRangeFilter
            column="dateOfCall"
            value={dateRangeFilter}
            onChange={onDateRangeChange}
          />
        </div>

        {/* Time of Call Filter */}
        <div className="filter-cell" style={{ width: '100px' }}>
          <ColumnFilter
            column="timeOfCall"
            value={columnFilters.timeOfCall || ''}
            onChange={(value) => onColumnFilterChange('timeOfCall', value)}
            placeholder="Filter time..."
          />
        </div>

        {/* Call Length Filter */}
        <div className="filter-cell" style={{ width: '100px' }}>
          <ColumnFilter
            column="callLength"
            value={columnFilters.callLength || ''}
            onChange={(value) => onColumnFilterChange('callLength', value)}
            placeholder="Filter length..."
          />
        </div>

        {/* Name Filter */}
        <div className="filter-cell" style={{ width: '150px' }}>
          <ColumnFilter
            column="name"
            value={columnFilters.name || ''}
            onChange={(value) => onColumnFilterChange('name', value)}
            placeholder="Filter name..."
          />
        </div>

        {/* Inbound/Outbound Filter */}
        <div className="filter-cell" style={{ width: '140px' }}>
          <ColumnFilter
            column="inboundOutbound"
            value={columnFilters.inboundOutbound || ''}
            onChange={(value) => onColumnFilterChange('inboundOutbound', value)}
            placeholder="Filter direction..."
          />
        </div>

        {/* Phone Number Filter */}
        <div className="filter-cell" style={{ width: '140px' }}>
          <ColumnFilter
            column="phoneNumber"
            value={columnFilters.phoneNumber || ''}
            onChange={(value) => onColumnFilterChange('phoneNumber', value)}
            placeholder="Filter phone..."
          />
        </div>

        {/* Notes Filter */}
        <div className="filter-cell" style={{ width: '200px' }}>
          <ColumnFilter
            column="notes"
            value={columnFilters.notes || ''}
            onChange={(value) => onColumnFilterChange('notes', value)}
            placeholder="Filter notes..."
          />
        </div>

        {/* Action Column - Clear Filters Button */}
        <div className="filter-cell" style={{ width: '100px' }}>
          <button
            onClick={onClearFilters}
            className={getThemeClass('clear-filters-btn')}
            title="Clear all filters"
          >
            Clear
          </button>
        </div>
      </div>
    </div>
  );
};
