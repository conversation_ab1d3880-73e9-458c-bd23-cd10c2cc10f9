import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';
import { ThemeProvider } from '../../../services/theme';
import { DataGrid, DataGridColumn } from '../DataGrid';

// Mock data for testing
interface TestRow {
  id: string;
  name: string;
  email: string;
  age: number;
  status: 'active' | 'inactive';
}

const mockData: TestRow[] = [
  { id: '1', name: '<PERSON>', email: '<EMAIL>', age: 30, status: 'active' },
  { id: '2', name: '<PERSON>', email: '<EMAIL>', age: 25, status: 'inactive' },
  { id: '3', name: '<PERSON>', email: '<EMAIL>', age: 35, status: 'active' },
];

const mockColumns: DataGridColumn<TestRow>[] = [
  {
    key: 'name',
    name: 'Name',
    width: 150,
    sortable: true,
    filterable: true,
  },
  {
    key: 'email',
    name: 'Email',
    width: 200,
    sortable: true,
    filterable: true,
  },
  {
    key: 'age',
    name: 'Age',
    width: 100,
    sortable: true,
  },
  {
    key: 'status',
    name: 'Status',
    width: 120,
    formatter: ({ row }) => (
      <span className={`status-${row.status}`}>
        {row.status}
      </span>
    ),
  },
];

// Test wrapper component
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <ThemeProvider defaultTheme="crm" enableAutoDetection={false}>
    {children}
  </ThemeProvider>
);

describe('DataGrid Component', () => {
  const user = userEvent.setup();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Basic Rendering', () => {
    it('renders without crashing', () => {
      render(
        <TestWrapper>
          <DataGrid columns={mockColumns} rows={mockData} />
        </TestWrapper>
      );
      
      expect(screen.getByRole('grid')).toBeInTheDocument();
    });

    it('displays column headers correctly', () => {
      render(
        <TestWrapper>
          <DataGrid columns={mockColumns} rows={mockData} />
        </TestWrapper>
      );
      
      expect(screen.getByText('Name')).toBeInTheDocument();
      expect(screen.getByText('Email')).toBeInTheDocument();
      expect(screen.getByText('Age')).toBeInTheDocument();
      expect(screen.getByText('Status')).toBeInTheDocument();
    });

    it('displays row data correctly', () => {
      render(
        <TestWrapper>
          <DataGrid columns={mockColumns} rows={mockData} />
        </TestWrapper>
      );
      
      expect(screen.getByText('John Doe')).toBeInTheDocument();
      expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
      expect(screen.getByText('30')).toBeInTheDocument();
      expect(screen.getByText('active')).toBeInTheDocument();
    });

    it('renders custom formatters correctly', () => {
      render(
        <TestWrapper>
          <DataGrid columns={mockColumns} rows={mockData} />
        </TestWrapper>
      );
      
      const statusElements = screen.getAllByText('active');
      expect(statusElements[0]).toHaveClass('status-active');
    });
  });

  describe('Loading State', () => {
    it('displays loading state when loading prop is true', () => {
      render(
        <TestWrapper>
          <DataGrid columns={mockColumns} rows={[]} loading={true} />
        </TestWrapper>
      );
      
      expect(screen.getByText('Loading data...')).toBeInTheDocument();
    });

    it('hides loading state when loading prop is false', () => {
      render(
        <TestWrapper>
          <DataGrid columns={mockColumns} rows={mockData} loading={false} />
        </TestWrapper>
      );
      
      expect(screen.queryByText('Loading data...')).not.toBeInTheDocument();
    });
  });

  describe('Empty State', () => {
    it('displays default empty state when no rows', () => {
      render(
        <TestWrapper>
          <DataGrid columns={mockColumns} rows={[]} />
        </TestWrapper>
      );
      
      expect(screen.getByText('No data available')).toBeInTheDocument();
    });

    it('displays custom empty state when provided', () => {
      const customEmptyState = <div>Custom empty message</div>;
      
      render(
        <TestWrapper>
          <DataGrid 
            columns={mockColumns} 
            rows={[]} 
            noRowsFallback={customEmptyState}
          />
        </TestWrapper>
      );
      
      expect(screen.getByText('Custom empty message')).toBeInTheDocument();
    });
  });

  describe('Filtering', () => {
    it('shows filter inputs when filtering is enabled', () => {
      render(
        <TestWrapper>
          <DataGrid 
            columns={mockColumns} 
            rows={mockData} 
            enableFiltering={true}
          />
        </TestWrapper>
      );
      
      expect(screen.getByPlaceholderText('Filter Name...')).toBeInTheDocument();
      expect(screen.getByPlaceholderText('Filter Email...')).toBeInTheDocument();
    });

    it('filters data based on input', async () => {
      render(
        <TestWrapper>
          <DataGrid 
            columns={mockColumns} 
            rows={mockData} 
            enableFiltering={true}
          />
        </TestWrapper>
      );
      
      const nameFilter = screen.getByPlaceholderText('Filter Name...');
      await user.type(nameFilter, 'John');
      
      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument();
        expect(screen.queryByText('Jane Smith')).not.toBeInTheDocument();
      });
    });

    it('hides filter inputs when filtering is disabled', () => {
      render(
        <TestWrapper>
          <DataGrid 
            columns={mockColumns} 
            rows={mockData} 
            enableFiltering={false}
          />
        </TestWrapper>
      );
      
      expect(screen.queryByPlaceholderText('Filter Name...')).not.toBeInTheDocument();
    });
  });

  describe('Row Interactions', () => {
    it('calls onRowClick when row is clicked', async () => {
      const mockOnRowClick = jest.fn();
      
      render(
        <TestWrapper>
          <DataGrid 
            columns={mockColumns} 
            rows={mockData} 
            onRowClick={mockOnRowClick}
          />
        </TestWrapper>
      );
      
      const firstRow = screen.getByText('John Doe').closest('[role="row"]');
      if (firstRow) {
        await user.click(firstRow);
        expect(mockOnRowClick).toHaveBeenCalledWith(mockData[0], 0);
      }
    });

    it('calls onRowDoubleClick when row is double-clicked', async () => {
      const mockOnRowDoubleClick = jest.fn();
      
      render(
        <TestWrapper>
          <DataGrid 
            columns={mockColumns} 
            rows={mockData} 
            onRowDoubleClick={mockOnRowDoubleClick}
          />
        </TestWrapper>
      );
      
      const firstRow = screen.getByText('John Doe').closest('[role="row"]');
      if (firstRow) {
        await user.dblClick(firstRow);
        expect(mockOnRowDoubleClick).toHaveBeenCalledWith(mockData[0], 0);
      }
    });
  });

  describe('Selection', () => {
    it('shows selection column when selection is enabled', () => {
      render(
        <TestWrapper>
          <DataGrid 
            columns={mockColumns} 
            rows={mockData} 
            enableSelection={true}
          />
        </TestWrapper>
      );
      
      const checkboxes = screen.getAllByRole('checkbox');
      expect(checkboxes.length).toBeGreaterThan(0);
    });

    it('handles row selection', async () => {
      const mockOnSelectedRowsChange = jest.fn();
      
      render(
        <TestWrapper>
          <DataGrid 
            columns={mockColumns} 
            rows={mockData} 
            enableSelection={true}
            onSelectedRowsChange={mockOnSelectedRowsChange}
          />
        </TestWrapper>
      );
      
      const checkboxes = screen.getAllByRole('checkbox');
      if (checkboxes[1]) { // First data row checkbox (index 0 is header)
        await user.click(checkboxes[1]);
        expect(mockOnSelectedRowsChange).toHaveBeenCalled();
      }
    });
  });

  describe('Theme Integration', () => {
    it('applies CRM theme classes', () => {
      render(
        <ThemeProvider defaultTheme="crm" enableAutoDetection={false}>
          <DataGrid columns={mockColumns} rows={mockData} />
        </ThemeProvider>
      );
      
      const gridContainer = screen.getByRole('grid').closest('.data-grid');
      expect(gridContainer).toHaveClass('data-grid');
    });

    it('applies MFE theme classes', () => {
      render(
        <ThemeProvider defaultTheme="mfe" enableAutoDetection={false}>
          <DataGrid columns={mockColumns} rows={mockData} />
        </ThemeProvider>
      );
      
      const gridContainer = screen.getByRole('grid').closest('.data-grid');
      expect(gridContainer).toHaveClass('data-grid');
    });
  });

  describe('Accessibility', () => {
    it('has proper ARIA roles', () => {
      render(
        <TestWrapper>
          <DataGrid columns={mockColumns} rows={mockData} />
        </TestWrapper>
      );
      
      expect(screen.getByRole('grid')).toBeInTheDocument();
      expect(screen.getAllByRole('row')).toHaveLength(mockData.length + 1); // +1 for header
    });

    it('supports keyboard navigation', async () => {
      render(
        <TestWrapper>
          <DataGrid columns={mockColumns} rows={mockData} />
        </TestWrapper>
      );
      
      const grid = screen.getByRole('grid');
      grid.focus();
      
      // Test that the grid can receive focus
      expect(grid).toHaveFocus();
    });
  });

  describe('Error Handling', () => {
    it('handles empty columns array gracefully', () => {
      render(
        <TestWrapper>
          <DataGrid columns={[]} rows={mockData} />
        </TestWrapper>
      );
      
      // Should not crash and should show empty state
      expect(screen.getByText('No data available')).toBeInTheDocument();
    });

    it('handles malformed data gracefully', () => {
      const malformedData = [
        { id: '1', name: null, email: undefined },
      ] as any;
      
      render(
        <TestWrapper>
          <DataGrid columns={mockColumns} rows={malformedData} />
        </TestWrapper>
      );
      
      // Should not crash
      expect(screen.getByRole('grid')).toBeInTheDocument();
    });
  });
});
