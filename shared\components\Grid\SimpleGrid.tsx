import React, { useMemo } from 'react';
import { useThemeStyles } from '../../services/theme';
import { GridProps, GridColumn } from './types';
import './Grid.css';

export function SimpleGrid<T = any>(props: GridProps<T>) {
  const {
    data = [],
    columns,
    className = '',
    style,
    height = '100%',
    width = '100%',
    theme = 'crm',
    loading = false,
    emptyComponent,
    loadingComponent,
    onRowClick,
    onRowDoubleClick
  } = props;

  const { getThemeClass } = useThemeStyles();

  // Grid classes
  const gridClasses = useMemo(() => {
    const classes = [
      'grid',
      `grid--theme-${theme}`,
      'grid--simple',
      className
    ];

    if (loading) classes.push('grid--loading');

    return classes.join(' ');
  }, [theme, className, loading]);

  // Loading state
  if (loading) {
    return (
      <div className={getThemeClass(gridClasses)} style={{ height, width, ...style }}>
        <div className="grid__loading-overlay">
          {loadingComponent || <div>Loading...</div>}
        </div>
      </div>
    );
  }

  // Empty state
  if (data.length === 0) {
    return (
      <div className={getThemeClass(gridClasses)} style={{ height, width, ...style }}>
        <div className="grid__empty-state">
          {emptyComponent || <div>No data available</div>}
        </div>
      </div>
    );
  }

  return (
    <div
      className={getThemeClass(gridClasses)}
      style={{ height, width, ...style }}
      role="grid"
    >
      {/* Header */}
      <div className="grid__header">
        <div className="grid__header-row" role="row">
          {columns.map((column, index) => (
            <div
              key={String(column.key)}
              className="grid__header-cell"
              style={{ width: column.width || 120 }}
              role="columnheader"
              aria-colindex={index + 1}
            >
              <span className="grid__header-title">{column.title}</span>
            </div>
          ))}
        </div>
      </div>

      {/* Body */}
      <div className="grid__body">
        {data.map((row, rowIndex) => (
          <div
            key={rowIndex}
            className="grid__row"
            role="row"
            aria-rowindex={rowIndex + 2}
            onClick={(e) => onRowClick?.(row, rowIndex, e)}
            onDoubleClick={(e) => onRowDoubleClick?.(row, rowIndex, e)}
          >
            {columns.map((column, columnIndex) => {
              const value = (row as any)[column.key];
              return (
                <div
                  key={String(column.key)}
                  className="grid__cell"
                  style={{ width: column.width || 120 }}
                  role="gridcell"
                  aria-colindex={columnIndex + 1}
                >
                  <div className="grid__cell-content">
                    {column.renderer ? (
                      column.renderer({
                        value,
                        row,
                        column,
                        rowIndex,
                        columnIndex,
                        isSelected: false,
                        isEditing: false
                      })
                    ) : (
                      String(value || '')
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        ))}
      </div>
    </div>
  );
}

// Export with display name for debugging
SimpleGrid.displayName = 'SimpleGrid';
