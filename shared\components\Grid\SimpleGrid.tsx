import React, { useMemo, useState, useCallback } from 'react';
import { useThemeStyles } from '../../services/theme';
import { GridProps, GridColumn, FilterValue, SortDirection } from './types';
import { ColumnFilterInput } from './components/ColumnFilterInput';
import { SimplePagination } from './components/SimplePagination';
import './Grid.css';

export function SimpleGrid<T = any>(props: GridProps<T>) {
  const {
    data = [],
    columns,
    className = '',
    style,
    height = '100%',
    width = '100%',
    theme = 'crm',
    loading = false,
    emptyComponent,
    loadingComponent,
    onRowClick,
    onRowDoubleClick
  } = props;

  const { getThemeClass } = useThemeStyles();

  // Filtering state
  const [columnFilters, setColumnFilters] = useState<Record<string, FilterValue>>({});
  const [showFilters, setShowFilters] = useState(false);

  // Sorting state
  const [sortColumn, setSortColumn] = useState<string | null>(null);
  const [sortDirection, setSortDirection] = useState<SortDirection>(null);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);

  // Selection state
  const [selectedRows, setSelectedRows] = useState<Set<string | number>>(new Set());
  const [selectionMode] = useState<'none' | 'single' | 'multiple'>('multiple'); // Can be made configurable

  // Filter handlers
  const handleColumnFilter = useCallback((columnKey: string, filter: FilterValue) => {
    setColumnFilters(prev => ({
      ...prev,
      [columnKey]: filter
    }));
  }, []);

  const handleClearFilters = useCallback(() => {
    setColumnFilters({});
  }, []);

  // Sort handler
  const handleSort = useCallback((columnKey: string) => {
    if (sortColumn === columnKey) {
      // Toggle sort direction
      if (sortDirection === 'asc') {
        setSortDirection('desc');
      } else if (sortDirection === 'desc') {
        setSortColumn(null);
        setSortDirection(null);
      } else {
        setSortDirection('asc');
      }
    } else {
      setSortColumn(columnKey);
      setSortDirection('asc');
    }
  }, [sortColumn, sortDirection]);

  // Apply filters and sorting to data
  const processedData = useMemo(() => {
    let filtered = [...data];

    // Apply column filters
    Object.entries(columnFilters).forEach(([columnKey, filter]) => {
      if (!filter.value) return;

      filtered = filtered.filter(row => {
        const value = (row as any)[columnKey];
        const { operator, value: filterValue } = filter;

        switch (operator) {
          case 'contains':
            return String(value).toLowerCase().includes(String(filterValue).toLowerCase());
          case 'equals':
            return value === filterValue;
          case 'startsWith':
            return String(value).toLowerCase().startsWith(String(filterValue).toLowerCase());
          case 'endsWith':
            return String(value).toLowerCase().endsWith(String(filterValue).toLowerCase());
          case 'gt':
            return Number(value) > Number(filterValue);
          case 'lt':
            return Number(value) < Number(filterValue);
          case 'gte':
            return Number(value) >= Number(filterValue);
          case 'lte':
            return Number(value) <= Number(filterValue);
          case 'in':
            return Array.isArray(filterValue) && filterValue.includes(value);
          default:
            return true;
        }
      });
    });

    // Apply sorting
    if (sortColumn && sortDirection) {
      filtered.sort((a, b) => {
        const aValue = (a as any)[sortColumn];
        const bValue = (b as any)[sortColumn];

        let result = 0;
        if (aValue < bValue) result = -1;
        else if (aValue > bValue) result = 1;

        return sortDirection === 'desc' ? -result : result;
      });
    }

    return filtered;
  }, [data, columnFilters, sortColumn, sortDirection]);

  // Apply pagination
  const paginatedData = useMemo(() => {
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    return processedData.slice(startIndex, endIndex);
  }, [processedData, currentPage, pageSize]);

  // Calculate pagination info
  const totalPages = Math.ceil(processedData.length / pageSize);
  const totalRecords = processedData.length;
  const startRecord = Math.min((currentPage - 1) * pageSize + 1, totalRecords);
  const endRecord = Math.min(currentPage * pageSize, totalRecords);

  // Pagination handlers
  const handlePageChange = useCallback((page: number) => {
    setCurrentPage(Math.max(1, Math.min(page, totalPages)));
  }, [totalPages]);

  const handlePageSizeChange = useCallback((newPageSize: number) => {
    setPageSize(newPageSize);
    setCurrentPage(1); // Reset to first page
  }, []);

  // Selection handlers
  const getRowKey = useCallback((row: T, index: number): string | number => {
    return (row as any).id || index;
  }, []);

  const handleRowSelection = useCallback((row: T, index: number, event: React.MouseEvent) => {
    if (selectionMode === 'none') return;

    const rowKey = getRowKey(row, index);

    if (selectionMode === 'single') {
      setSelectedRows(new Set([rowKey]));
    } else if (selectionMode === 'multiple') {
      if (event.ctrlKey || event.metaKey) {
        // Toggle selection
        setSelectedRows(prev => {
          const newSet = new Set(prev);
          if (newSet.has(rowKey)) {
            newSet.delete(rowKey);
          } else {
            newSet.add(rowKey);
          }
          return newSet;
        });
      } else {
        // Single selection
        setSelectedRows(new Set([rowKey]));
      }
    }
  }, [selectionMode, getRowKey]);

  const handleSelectAll = useCallback(() => {
    if (selectionMode !== 'multiple') return;

    const allRowKeys = paginatedData.map((row, index) => getRowKey(row, index));
    setSelectedRows(new Set(allRowKeys));
  }, [selectionMode, paginatedData, getRowKey]);

  const handleDeselectAll = useCallback(() => {
    setSelectedRows(new Set());
  }, []);

  // Check if all rows are selected
  const isAllSelected = selectionMode === 'multiple' &&
    paginatedData.length > 0 &&
    paginatedData.every((row, index) => selectedRows.has(getRowKey(row, index)));

  const isPartiallySelected = selectionMode === 'multiple' &&
    selectedRows.size > 0 &&
    !isAllSelected;

  // Keyboard navigation
  const [focusedRowIndex, setFocusedRowIndex] = useState<number>(-1);

  const handleKeyDown = useCallback((event: React.KeyboardEvent) => {
    switch (event.key) {
      case 'ArrowDown':
        event.preventDefault();
        setFocusedRowIndex(prev => Math.min(prev + 1, paginatedData.length - 1));
        break;
      case 'ArrowUp':
        event.preventDefault();
        setFocusedRowIndex(prev => Math.max(prev - 1, 0));
        break;
      case ' ':
      case 'Enter':
        event.preventDefault();
        if (focusedRowIndex >= 0 && focusedRowIndex < paginatedData.length) {
          const row = paginatedData[focusedRowIndex];
          if (event.key === ' ') {
            // Space for selection
            handleRowSelection(row, focusedRowIndex, event as any);
          } else {
            // Enter for row action (double click)
            onRowDoubleClick?.(row, focusedRowIndex, event as any);
          }
        }
        break;
      case 'Escape':
        setFocusedRowIndex(-1);
        break;
    }
  }, [focusedRowIndex, paginatedData, handleRowSelection, onRowDoubleClick]);

  // Grid classes
  const gridClasses = useMemo(() => {
    const classes = [
      'grid',
      `grid--theme-${theme}`,
      'grid--simple',
      className
    ];

    if (loading) classes.push('grid--loading');

    return classes.join(' ');
  }, [theme, className, loading]);

  // Loading state
  if (loading) {
    return (
      <div className={getThemeClass(gridClasses)} style={{ height, width, ...style }}>
        <div className="grid__loading-overlay">
          {loadingComponent || <div>Loading...</div>}
        </div>
      </div>
    );
  }

  // Empty state
  if (processedData.length === 0 && !loading) {
    return (
      <div className={getThemeClass(gridClasses)} style={{ height, width, ...style }}>
        <div className="grid__empty-state">
          {emptyComponent || <div>No data available</div>}
        </div>
      </div>
    );
  }

  return (
    <div
      className={getThemeClass(gridClasses)}
      style={{ height, width, ...style }}
      role="grid"
    >
      {/* Header */}
      <div className="grid__header">
        {/* Header Row with Sorting */}
        <div className="grid__header-row" role="row">
          {/* Selection Column Header */}
          {selectionMode !== 'none' && (
            <div className="grid__header-cell grid__header-cell--selection">
              {selectionMode === 'multiple' && (
                <input
                  type="checkbox"
                  checked={isAllSelected}
                  ref={input => {
                    if (input) input.indeterminate = isPartiallySelected;
                  }}
                  onChange={(e) => {
                    if (e.target.checked) {
                      handleSelectAll();
                    } else {
                      handleDeselectAll();
                    }
                  }}
                  aria-label="Select all rows"
                />
              )}
            </div>
          )}

          {columns.map((column, index) => {
            const isSorted = sortColumn === String(column.key);
            const sortIcon = isSorted ? (sortDirection === 'asc' ? ' ↑' : ' ↓') : '';

            return (
              <div
                key={String(column.key)}
                className={`grid__header-cell ${column.sortable !== false ? 'grid__header-cell--sortable' : ''} ${isSorted ? 'grid__header-cell--sorted' : ''}`}
                style={{ width: column.width || 120 }}
                role="columnheader"
                aria-colindex={index + 1}
                aria-sort={isSorted ? (sortDirection === 'asc' ? 'ascending' : 'descending') : 'none'}
                onClick={column.sortable !== false ? () => handleSort(String(column.key)) : undefined}
              >
                <div className="grid__header-content">
                  <span className="grid__header-title">
                    {column.title}{sortIcon}
                  </span>
                  {column.filterable !== false && (
                    <button
                      className="grid__filter-toggle"
                      onClick={(e) => {
                        e.stopPropagation();
                        setShowFilters(!showFilters);
                      }}
                      aria-label="Toggle filters"
                      title="Toggle filters"
                    >
                      🔍
                    </button>
                  )}
                </div>
              </div>
            );
          })}
        </div>

        {/* Filter Row */}
        {showFilters && (
          <div className="grid__filter-row" role="row">
            {/* Selection Column Filter */}
            {selectionMode !== 'none' && (
              <div className="grid__filter-cell grid__filter-cell--selection">
                {/* Empty space for selection column */}
              </div>
            )}

            {columns.map((column) => (
              <div
                key={`filter-${String(column.key)}`}
                className="grid__filter-cell"
                style={{ width: column.width || 120 }}
              >
                {column.filterable !== false && column.key !== 'actions' ? (
                  <ColumnFilterInput
                    column={column}
                    value={columnFilters[String(column.key)] || { operator: 'contains', value: '' }}
                    onChange={(filter) => handleColumnFilter(String(column.key), filter)}
                    onClear={() => handleColumnFilter(String(column.key), { operator: 'contains', value: '' })}
                  />
                ) : column.key === 'actions' ? (
                  <button
                    onClick={handleClearFilters}
                    className="grid__clear-filters-btn"
                    title="Clear all filters"
                  >
                    Clear All
                  </button>
                ) : null}
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Body */}
      <div
        className="grid__body"
        onKeyDown={handleKeyDown}
        tabIndex={0}
        role="grid"
        aria-label="Data grid"
      >
        {paginatedData.map((row, rowIndex) => {
          const rowKey = getRowKey(row, rowIndex);
          const isSelected = selectedRows.has(rowKey);
          const isFocused = focusedRowIndex === rowIndex;

          return (
            <div
              key={rowIndex}
              className={`grid__row ${isSelected ? 'grid__row--selected' : ''} ${isFocused ? 'grid__row--focused' : ''}`}
              role="row"
              aria-rowindex={rowIndex + 2}
              aria-selected={isSelected}
              tabIndex={isFocused ? 0 : -1}
              onClick={(e) => {
                setFocusedRowIndex(rowIndex);
                handleRowSelection(row, rowIndex, e);
                onRowClick?.(row, rowIndex, e);
              }}
              onDoubleClick={(e) => onRowDoubleClick?.(row, rowIndex, e)}
              onFocus={() => setFocusedRowIndex(rowIndex)}
            >
              {/* Selection Column */}
              {selectionMode !== 'none' && (
                <div className="grid__cell grid__cell--selection">
                  {selectionMode === 'multiple' && (
                    <input
                      type="checkbox"
                      checked={isSelected}
                      onChange={(e) => {
                        e.stopPropagation();
                        handleRowSelection(row, rowIndex, e as any);
                      }}
                      aria-label={`Select row ${rowIndex + 1}`}
                      tabIndex={-1}
                    />
                  )}
                  {selectionMode === 'single' && (
                    <input
                      type="radio"
                      name="grid-selection"
                      checked={isSelected}
                      onChange={(e) => {
                        e.stopPropagation();
                        handleRowSelection(row, rowIndex, e as any);
                      }}
                      aria-label={`Select row ${rowIndex + 1}`}
                      tabIndex={-1}
                    />
                  )}
                </div>
              )}

              {columns.map((column, columnIndex) => {
              const value = (row as any)[column.key];
              return (
                <div
                  key={String(column.key)}
                  className="grid__cell"
                  style={{ width: column.width || 120 }}
                  role="gridcell"
                  aria-colindex={columnIndex + 1}
                >
                  <div className="grid__cell-content">
                    {column.renderer ? (
                      column.renderer({
                        value,
                        row,
                        column,
                        rowIndex,
                        columnIndex,
                        isSelected: false,
                        isEditing: false
                      })
                    ) : (
                      String(value || '')
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <SimplePagination
          currentPage={currentPage}
          totalPages={totalPages}
          totalRecords={totalRecords}
          pageSize={pageSize}
          startRecord={startRecord}
          endRecord={endRecord}
          onPageChange={handlePageChange}
          onPageSizeChange={handlePageSizeChange}
          showPageInfo={true}
          showPageSizeSelector={true}
          maxPageNumbers={5}
        />
      )}
    </div>
  );
}

// Export with display name for debugging
SimpleGrid.displayName = 'SimpleGrid';
