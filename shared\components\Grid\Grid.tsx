import React, { useRef, useEffect, useMemo } from 'react';
import { useThemeStyles } from '../../services/theme';
import { GridProps } from './types';
import { useGridState } from './hooks/useGridState';
import { useResponsive, useResponsiveColumns, useResponsiveFeatures } from './hooks/useResponsive';
import { GridHeader } from './components/GridHeader';
import { GridBody } from './components/GridBody';
import { GridPagination } from './components/GridPagination';
import { GridToolbar } from './components/GridToolbar';
import { GridLoadingOverlay } from './components/GridLoadingOverlay';
import { GridEmptyState } from './components/GridEmptyState';
import { GridErrorState } from './components/GridErrorState';
import './Grid.css';

export function Grid<T = any>(props: GridProps<T>) {
  const {
    className = '',
    style,
    height = '100%',
    width = '100%',
    theme = 'crm',
    features = {},
    stickyHeader = true,
    stickyColumns = false,
    zebra = true,
    bordered = true,
    compact = false,
    virtualized = true,
    loading: externalLoading = false,
    loadingComponent,
    emptyComponent,
    errorComponent,
    customComponents = {},
    ariaLabel = 'Data grid',
    ariaDescription
  } = props;

  const { getThemeClass } = useThemeStyles();
  const containerRef = useRef<HTMLDivElement>(null);
  const { state, actions } = useGridState(props);

  // Responsive behavior
  const responsive = useResponsive(state.columns, {
    breakpoints: { mobile: 768, tablet: 1024, desktop: 1200 },
    hiddenColumns: {
      mobile: ['notes', 'callLength', 'timeOfCall'],
      tablet: ['notes']
    },
    stackedLayout: features.responsive !== false,
    compactMode: compact
  });

  // Get container width for responsive calculations
  const [containerWidth, setContainerWidth] = React.useState(0);
  
  useEffect(() => {
    if (!containerRef.current) return;
    
    const resizeObserver = new ResizeObserver(entries => {
      for (const entry of entries) {
        setContainerWidth(entry.contentRect.width);
      }
    });
    
    resizeObserver.observe(containerRef.current);
    return () => resizeObserver.disconnect();
  }, []);

  // Responsive columns
  const responsiveColumns = useResponsiveColumns(
    state.visibleColumns,
    containerWidth,
    responsive
  );

  // Responsive features
  const responsiveFeatures = useResponsiveFeatures(responsive);

  // Merge features with responsive adjustments
  const mergedFeatures = useMemo(() => ({
    sorting: true,
    filtering: true,
    pagination: true,
    selection: 'none',
    editing: 'none',
    export: false,
    grouping: false,
    virtualization: virtualized,
    responsive: true,
    accessibility: true,
    ...features,
    ...responsiveFeatures
  }), [features, responsiveFeatures, virtualized]);

  // Grid classes
  const gridClasses = useMemo(() => {
    const classes = [
      'grid',
      `grid--theme-${theme}`,
      className
    ];

    if (responsive.isMobile) classes.push('grid--mobile');
    if (responsive.isTablet) classes.push('grid--tablet');
    if (responsive.isDesktop) classes.push('grid--desktop');
    if (responsive.shouldStack) classes.push('grid--stacked');
    if (responsive.shouldCompact) classes.push('grid--compact');
    if (stickyHeader) classes.push('grid--sticky-header');
    if (stickyColumns) classes.push('grid--sticky-columns');
    if (zebra) classes.push('grid--zebra');
    if (bordered) classes.push('grid--bordered');
    if (compact) classes.push('grid--compact-mode');
    if (state.loading || externalLoading) classes.push('grid--loading');

    return classes.join(' ');
  }, [
    theme, className, responsive, stickyHeader, stickyColumns, 
    zebra, bordered, compact, state.loading, externalLoading
  ]);

  // Loading state
  const isLoading = state.loading || externalLoading;

  // Error state
  if (state.error) {
    return (
      <div className={getThemeClass(gridClasses)} style={style}>
        {errorComponent || <GridErrorState error={state.error} onRetry={() => actions.setError(null)} />}
      </div>
    );
  }

  // Empty state
  if (!isLoading && state.sortedData.length === 0) {
    return (
      <div className={getThemeClass(gridClasses)} style={style}>
        {emptyComponent || <GridEmptyState />}
      </div>
    );
  }

  return (
    <div
      ref={containerRef}
      className={getThemeClass(gridClasses)}
      style={{ height, width, ...style }}
      role="grid"
      aria-label={ariaLabel}
      aria-description={ariaDescription}
      aria-rowcount={state.totalRows}
      aria-colcount={responsiveColumns.length}
    >
      {/* Toolbar - TODO: Enable when GridToolbar is ready */}
      {/* {mergedFeatures.export || mergedFeatures.filtering?.globalSearch && (
        <GridToolbar
          state={state}
          actions={actions}
          features={mergedFeatures}
          responsive={responsive}
          customComponent={customComponents.toolbar}
        />
      )} */}

      {/* Grid Container */}
      <div className="grid__container">
        {/* Header */}
        <GridHeader
          columns={responsiveColumns}
          state={state}
          actions={actions}
          features={mergedFeatures}
          responsive={responsive}
          customComponent={customComponents.header}
        />

        {/* Body */}
        <div className="grid__body-container">
          <GridBody
            columns={responsiveColumns}
            state={state}
            actions={actions}
            features={mergedFeatures}
            responsive={responsive}
            onRowClick={props.onRowClick}
            onRowDoubleClick={props.onRowDoubleClick}
            onCellClick={props.onCellClick}
            onCellEdit={props.onCellEdit}
            onRowExpand={props.onRowExpand}
            customRowComponent={customComponents.row}
            customCellComponent={customComponents.cell}
          />
        </div>

        {/* Loading Overlay */}
        {isLoading && (
          <GridLoadingOverlay
            customComponent={loadingComponent}
          />
        )}
      </div>

      {/* Pagination - TODO: Enable when GridPagination is ready */}
      {/* {mergedFeatures.pagination && state.totalPages > 1 && (
        <GridPagination
          state={state}
          actions={actions}
          features={mergedFeatures.pagination}
          responsive={responsive}
          customComponent={customComponents.pagination}
        />
      )} */}
    </div>
  );
}

// Export with display name for debugging
Grid.displayName = 'Grid';
