import React from 'react';
import { useThemeStyles } from '../../services/theme';
import { ColumnFilter, DateRangeFilter, DateRangeValue } from './ColumnFilter';
import './FilterRow.css';

export interface FilterRowProps {
  columnFilters: Record<string, string>;
  dateRangeFilter: DateRangeValue;
  onColumnFilterChange: (column: string, value: string) => void;
  onDateRangeChange: (value: DateRangeValue) => void;
  onClearFilters: () => void;
  columns: Array<{
    key: string;
    name: string;
    width: number;
    filterType?: 'text' | 'date-range';
    placeholder?: string;
  }>;
}

export const FilterRow: React.FC<FilterRowProps> = ({
  columnFilters,
  dateRangeFilter,
  onColumnFilterChange,
  onDateRangeChange,
  onClearFilters,
  columns
}) => {
  const { getThemeClass } = useThemeStyles();

  return (
    <div className={getThemeClass('filter-row')}>
      <div className="filter-row-content">
        {columns.map((column) => (
          <div 
            key={column.key} 
            className="filter-cell" 
            style={{ width: `${column.width}px` }}
          >
            {column.filterType === 'date-range' ? (
              <DateRangeFilter
                column={column.key}
                value={dateRangeFilter}
                onChange={onDateRangeChange}
              />
            ) : column.key === 'actions' ? (
              <button
                onClick={onClearFilters}
                className={getThemeClass('clear-filters-btn')}
                title="Clear all filters"
              >
                Clear
              </button>
            ) : (
              <ColumnFilter
                column={column.key}
                value={columnFilters[column.key] || ''}
                onChange={(value) => onColumnFilterChange(column.key, value)}
                placeholder={column.placeholder || `Filter ${column.name.toLowerCase()}...`}
              />
            )}
          </div>
        ))}
      </div>
    </div>
  );
};
