/* Column Filter Input */
.column-filter-input {
  width: 100%;
  padding: var(--theme-spacing-xs, 4px) var(--theme-spacing-sm, 8px);
  border: 1px solid var(--theme-border-color, #e1e5e9);
  border-radius: var(--theme-border-radius-sm, 2px);
  font-size: var(--theme-font-size-sm, 12px);
  font-family: var(--theme-font-family, 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif);
  background: var(--theme-input-background, #ffffff);
  color: var(--theme-text-color, #323130);
  outline: none;
  transition: border-color 0.2s ease;
}

.column-filter-input:focus {
  border-color: var(--theme-primary-color, #0078d4);
  box-shadow: 0 0 0 1px var(--theme-primary-color, #0078d4);
}

.column-filter-input::placeholder {
  color: var(--theme-text-muted, #605e5c);
  font-style: italic;
}

/* Date Range Filter */
.date-range-filter {
  display: flex;
  flex-direction: column;
  gap: var(--theme-spacing-xs, 4px);
  width: 100%;
}

.date-range-input-group {
  display: flex;
  align-items: center;
  gap: var(--theme-spacing-xs, 4px);
}

.date-range-label {
  font-size: var(--theme-font-size-xs, 11px);
  font-weight: 600;
  color: var(--theme-text-color, #323130);
  min-width: 35px;
  text-align: right;
}

.date-range-input {
  flex: 1;
  padding: var(--theme-spacing-xs, 4px) var(--theme-spacing-sm, 8px);
  border: 1px solid var(--theme-border-color, #e1e5e9);
  border-radius: var(--theme-border-radius-sm, 2px);
  font-size: var(--theme-font-size-sm, 12px);
  font-family: var(--theme-font-family, 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif);
  background: var(--theme-input-background, #ffffff);
  color: var(--theme-text-color, #323130);
  outline: none;
  transition: border-color 0.2s ease;
}

.date-range-input:focus {
  border-color: var(--theme-primary-color, #0078d4);
  box-shadow: 0 0 0 1px var(--theme-primary-color, #0078d4);
}

/* Responsive Design */
@media (max-width: 768px) {
  .date-range-filter {
    gap: var(--theme-spacing-xs, 4px);
  }
  
  .date-range-input-group {
    flex-direction: column;
    align-items: stretch;
  }
  
  .date-range-label {
    text-align: left;
    min-width: auto;
  }
}

/* Theme-specific overrides */
.theme-crm .column-filter-input:focus {
  border-color: var(--crm-primary-color, #0078d4);
  box-shadow: 0 0 0 1px var(--crm-primary-color, #0078d4);
}

.theme-crm .date-range-input:focus {
  border-color: var(--crm-primary-color, #0078d4);
  box-shadow: 0 0 0 1px var(--crm-primary-color, #0078d4);
}

.theme-mfe .column-filter-input:focus {
  border-color: var(--mfe-primary-color, #4caf50);
  box-shadow: 0 0 0 1px var(--mfe-primary-color, #4caf50);
}

.theme-mfe .date-range-input:focus {
  border-color: var(--mfe-primary-color, #4caf50);
  box-shadow: 0 0 0 1px var(--mfe-primary-color, #4caf50);
}
