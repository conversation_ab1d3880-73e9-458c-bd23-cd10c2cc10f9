/* Filter Row */
.filter-row {
  background: var(--theme-bg-secondary, #f8f9fa);
  border-bottom: 1px solid var(--theme-border-color, #e1e5e9);
  padding: var(--theme-spacing-sm, 8px) var(--theme-spacing-lg, 24px);
}

.filter-row-content {
  display: flex;
  align-items: center;
  gap: 1px;
  background: var(--theme-border-color, #e1e5e9);
}

.filter-cell {
  background: var(--theme-bg-primary, #ffffff);
  padding: var(--theme-spacing-sm, 8px);
  display: flex;
  align-items: center;
  min-height: 40px;
}

.filter-cell .column-filter-input {
  width: 100%;
  border: 1px solid var(--theme-border-color, #e1e5e9);
  border-radius: var(--theme-border-radius-sm, 2px);
  padding: var(--theme-spacing-xs, 4px) var(--theme-spacing-sm, 8px);
  font-size: var(--theme-font-size-sm, 12px);
  background: var(--theme-input-background, #ffffff);
  color: var(--theme-text-color, #323130);
  outline: none;
  transition: border-color 0.2s ease;
}

.filter-cell .column-filter-input:focus {
  border-color: var(--theme-primary-color, #0078d4);
  box-shadow: 0 0 0 1px var(--theme-primary-color, #0078d4);
}

.filter-cell .column-filter-input::placeholder {
  color: var(--theme-text-muted, #605e5c);
  font-style: italic;
}

/* Date Range Filter in Filter Row */
.filter-cell .date-range-filter {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: var(--theme-spacing-xs, 2px);
}

.filter-cell .date-range-input-group {
  display: flex;
  align-items: center;
  gap: var(--theme-spacing-xs, 2px);
}

.filter-cell .date-range-label {
  font-size: var(--theme-font-size-xs, 10px);
  font-weight: 600;
  color: var(--theme-text-color, #323130);
  min-width: 25px;
  text-align: right;
}

.filter-cell .date-range-input {
  flex: 1;
  padding: var(--theme-spacing-xs, 2px) var(--theme-spacing-xs, 4px);
  border: 1px solid var(--theme-border-color, #e1e5e9);
  border-radius: var(--theme-border-radius-sm, 2px);
  font-size: var(--theme-font-size-xs, 10px);
  background: var(--theme-input-background, #ffffff);
  color: var(--theme-text-color, #323130);
  outline: none;
  transition: border-color 0.2s ease;
}

.filter-cell .date-range-input:focus {
  border-color: var(--theme-primary-color, #0078d4);
  box-shadow: 0 0 0 1px var(--theme-primary-color, #0078d4);
}

/* Clear Filters Button */
.clear-filters-btn {
  width: 100%;
  padding: var(--theme-spacing-xs, 4px) var(--theme-spacing-sm, 8px);
  background: var(--theme-secondary-color, #605e5c);
  color: #ffffff;
  border: 1px solid var(--theme-secondary-color, #605e5c);
  border-radius: var(--theme-border-radius-sm, 2px);
  font-size: var(--theme-font-size-sm, 12px);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.clear-filters-btn:hover {
  background: var(--theme-secondary-hover, #484644);
  border-color: var(--theme-secondary-hover, #484644);
}

.clear-filters-btn:active {
  transform: translateY(1px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .filter-row-content {
    flex-direction: column;
    gap: var(--theme-spacing-xs, 4px);
  }
  
  .filter-cell {
    width: 100% !important;
    min-height: 35px;
  }
  
  .filter-cell .date-range-filter {
    flex-direction: row;
    gap: var(--theme-spacing-sm, 8px);
  }
  
  .filter-cell .date-range-input-group {
    flex: 1;
  }
}

/* Theme-specific overrides */
.theme-crm .filter-row {
  background-color: var(--crm-header-background, #f8f9fa);
}

.theme-crm .clear-filters-btn {
  background-color: var(--crm-secondary-color, #605e5c);
}

.theme-crm .clear-filters-btn:hover {
  background-color: var(--crm-secondary-hover, #484644);
}

.theme-mfe .filter-row {
  background-color: var(--mfe-header-background, #fafafa);
}

.theme-mfe .clear-filters-btn {
  background-color: var(--mfe-secondary-color, #757575);
}

.theme-mfe .clear-filters-btn:hover {
  background-color: var(--mfe-secondary-hover, #616161);
}
