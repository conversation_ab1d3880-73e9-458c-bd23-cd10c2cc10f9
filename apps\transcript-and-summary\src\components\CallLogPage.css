/**
 * Call Log Page Styles
 * 
 * Theme-aware styles for the call log landing page
 */

.call-log-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  margin: 0;
  padding: 0;
  background: var(--theme-bg-primary, #ffffff);
  color: var(--theme-text-primary, #323130);
  overflow: hidden;
  font-family: var(--theme-font-family, 'Segoe UI', sans-serif);
}

.call-log-page--error {
  justify-content: center;
  align-items: center;
}

/* Header */
.call-log-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--theme-spacing-lg, 24px);
  border-bottom: 1px solid var(--theme-border-primary, #d1d1d1);
  background: var(--theme-bg-primary, #ffffff);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  z-index: 10;
}

.call-log-title {
  margin: 0;
  font-size: 28px;
  font-weight: 600;
  color: var(--theme-primary-color, #0078d4);
}

.call-log-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.search-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.search-input {
  padding: 8px 12px;
  border: 1px solid var(--theme-border-primary, #d1d1d1);
  border-radius: var(--theme-border-radius, 4px);
  background: var(--theme-bg-primary, #ffffff);
  color: var(--theme-text-primary, #323130);
  font-size: 14px;
  font-family: inherit;
  min-width: 200px;
}

.search-input:focus {
  outline: none;
  border-color: var(--theme-primary, #0078d4);
  box-shadow: 0 0 0 1px var(--theme-primary, #0078d4);
}

.search-input::placeholder {
  color: var(--theme-text-secondary, #605e5c);
}

/* Content */
.call-log-content {
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
}

.call-log-content {
  flex: 1;
  padding: var(--theme-spacing-lg, 24px);
  overflow: hidden;
}

.data-grid-wrapper {
  height: 100%;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-radius: var(--theme-border-radius, 4px);
  overflow: hidden;
}

.call-log-grid {
  height: 100%;
  border: none;
  background: var(--theme-background-color, #ffffff);
}

/* React Data Grid overrides */
.call-log-grid .rdg {
  border: none;
  font-family: var(--theme-font-family, 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif);
  height: 100%;
}

.call-log-grid .rdg-header-row {
  background-color: var(--theme-header-background, #f8f9fa);
  border-bottom: 2px solid var(--theme-border-color, #e1e5e9);
  font-weight: 600;
}

.call-log-grid .rdg-cell {
  border-right: none;
  padding: var(--theme-spacing-sm, 8px);
  border-bottom: 1px solid var(--theme-border-light, #f3f2f1);
}

.call-log-grid .rdg-row {
  border-bottom: 1px solid var(--theme-border-light, #f3f2f1);
}

.call-log-grid .rdg-row:hover {
  background-color: var(--theme-hover-background, #f5f5f5);
  cursor: pointer;
}

.call-log-grid .rdg-row:last-child {
  border-bottom: none;
}

/* Call Direction Indicators */
.call-direction {
  padding: 4px 8px;
  border-radius: var(--theme-border-radius-sm, 2px);
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.call-direction--inbound {
  background: var(--theme-success-bg, #dff6dd);
  color: var(--theme-success-text, #107c10);
  border: 1px solid var(--theme-success-border, #92c5f7);
}

.call-direction--outbound {
  background: var(--theme-info-bg, #deecf9);
  color: var(--theme-info-text, #0078d4);
  border: 1px solid var(--theme-info-border, #92c5f7);
}

/* Notes Cell */
.notes-cell {
  position: relative;
  width: 100%;
  height: 100%;
}

.notes-textarea {
  width: 100%;
  height: 100%;
  border: 1px solid transparent;
  background: transparent;
  color: var(--theme-text-primary, #323130);
  font-size: 13px;
  font-family: inherit;
  resize: none;
  padding: 4px 6px;
  border-radius: var(--theme-border-radius-sm, 2px);
}

.notes-textarea:focus {
  outline: none;
  border-color: var(--theme-primary, #0078d4);
  background: var(--theme-bg-primary, #ffffff);
  box-shadow: 0 0 0 1px var(--theme-primary, #0078d4);
}

.notes-textarea::placeholder {
  color: var(--theme-text-secondary, #605e5c);
  font-style: italic;
}

.notes-saving-indicator {
  position: absolute;
  top: 2px;
  right: 2px;
  background: var(--theme-primary, #0078d4);
  color: white;
  font-size: 10px;
  padding: 2px 4px;
  border-radius: 2px;
  pointer-events: none;
}

/* Action Button */
.action-button {
  min-width: 60px;
  color: #ffffff !important;
  background-color: var(--theme-primary-color, #0078d4);
  border: 1px solid var(--theme-primary-color, #0078d4);
}

.action-button:hover {
  background-color: var(--theme-primary-hover, #106ebe);
  border-color: var(--theme-primary-hover, #106ebe);
  color: #ffffff !important;
}

/* Pagination */
.pagination-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 20px;
  border-top: 1px solid var(--theme-border-primary, #d1d1d1);
}

.pagination-info {
  color: var(--theme-text-secondary, #605e5c);
  font-size: 14px;
}

.pagination-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
}

.pagination-button {
  min-width: 40px;
}

/* Error State */
.error-message {
  text-align: center;
  padding: 40px;
  background: var(--theme-bg-secondary, #f8f9fa);
  border: 1px solid var(--theme-border-primary, #d1d1d1);
  border-radius: var(--theme-border-radius, 4px);
}

.error-message h3 {
  margin: 0 0 16px 0;
  color: var(--theme-error-text, #d13438);
}

.error-message p {
  margin: 0 0 20px 0;
  color: var(--theme-text-secondary, #605e5c);
}

/* No Records Message */
.no-records-message {
  text-align: center;
  padding: 40px;
  color: var(--theme-text-secondary, #605e5c);
  font-style: italic;
}

/* Theme-specific overrides */

/* CRM Theme */
[data-theme="crm"] .call-log-page {
  --theme-success-bg: #dff6dd;
  --theme-success-text: #107c10;
  --theme-success-border: #92c5f7;
  --theme-info-bg: #deecf9;
  --theme-info-text: #0078d4;
  --theme-info-border: #92c5f7;
  --theme-error-text: #d13438;
}

/* MFE Theme */
[data-theme="mfe"] .call-log-page {
  --theme-success-bg: #dcfce7;
  --theme-success-text: #166534;
  --theme-success-border: #bbf7d0;
  --theme-info-bg: #dbeafe;
  --theme-info-text: #1d4ed8;
  --theme-info-border: #93c5fd;
  --theme-error-text: #dc2626;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .call-log-page {
    padding: 16px;
    gap: 16px;
  }
  
  .call-log-header {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }
  
  .call-log-actions {
    justify-content: space-between;
  }
  
  .search-container {
    flex: 1;
  }
  
  .search-input {
    flex: 1;
    min-width: 0;
  }
}

@media (max-width: 768px) {
  .call-log-page {
    padding: 12px;
    gap: 12px;
  }
  
  .call-log-title {
    font-size: 24px;
  }
  
  .call-log-actions {
    flex-direction: column;
    gap: 12px;
  }
  
  .search-container {
    width: 100%;
  }
  
  .pagination-controls {
    flex-direction: column;
    gap: 12px;
    text-align: center;
  }
  
  .pagination-buttons {
    justify-content: center;
    flex-wrap: wrap;
  }
}

@media (max-width: 480px) {
  .call-log-page {
    padding: 8px;
    gap: 8px;
  }
  
  .call-log-title {
    font-size: 20px;
  }
  
  .search-input {
    font-size: 16px; /* Prevent zoom on iOS */
  }
  
  .notes-textarea {
    font-size: 14px; /* Prevent zoom on iOS */
  }
  
  .pagination-buttons {
    gap: 4px;
  }
  
  .pagination-button {
    min-width: 36px;
    font-size: 12px;
  }
}
