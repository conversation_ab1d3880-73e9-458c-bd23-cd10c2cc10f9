/**
 * DataGrid Component Styles
 * 
 * Theme-aware styles for the reusable DataGrid component
 * Uses CSS custom properties for theme switching
 */

/* Base DataGrid Container */
.data-grid {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: var(--theme-bg-primary, #ffffff);
  border: 1px solid var(--theme-border-primary, #d1d1d1);
  border-radius: var(--theme-border-radius, 4px);
  overflow: hidden;
  font-family: var(--theme-font-family, 'Segoe UI', sans-serif);
}

/* Filter Row */
.data-grid__filter-row {
  display: flex;
  background: var(--theme-bg-secondary, #f8f9fa);
  border-bottom: 1px solid var(--theme-border-primary, #d1d1d1);
  min-height: 40px;
}

.data-grid__filter-cell {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border-right: 1px solid var(--theme-border-primary, #d1d1d1);
  flex: 1;
  min-width: 0;
}

.data-grid__filter-cell:last-child {
  border-right: none;
}

.data-grid__filter-input {
  width: 100%;
  padding: 4px 8px;
  border: 1px solid var(--theme-border-secondary, #e1e1e1);
  border-radius: var(--theme-border-radius-sm, 2px);
  background: var(--theme-bg-primary, #ffffff);
  color: var(--theme-text-primary, #323130);
  font-size: var(--theme-font-size-sm, 12px);
  font-family: inherit;
}

.data-grid__filter-input:focus {
  outline: none;
  border-color: var(--theme-primary, #0078d4);
  box-shadow: 0 0 0 1px var(--theme-primary, #0078d4);
}

.data-grid__filter-input::placeholder {
  color: var(--theme-text-secondary, #605e5c);
}

/* Grid Container */
.data-grid__container {
  flex: 1;
  overflow: hidden;
}

.data-grid__inner {
  height: 100%;
  border: none;
}

/* Loading State */
.data-grid__loading {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  background: var(--theme-bg-primary, #ffffff);
  border: 1px solid var(--theme-border-primary, #d1d1d1);
  border-radius: var(--theme-border-radius, 4px);
}

.data-grid__loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  color: var(--theme-text-secondary, #605e5c);
}

.data-grid__loading-spinner {
  width: 24px;
  height: 24px;
  border: 2px solid var(--theme-border-primary, #d1d1d1);
  border-top: 2px solid var(--theme-primary, #0078d4);
  border-radius: 50%;
  animation: data-grid-spin 1s linear infinite;
}

@keyframes data-grid-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Empty State */
.data-grid__empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  color: var(--theme-text-secondary, #605e5c);
  font-style: italic;
}

/* React Data Grid Theme Overrides */
.data-grid .rdg {
  border: none;
  background: var(--theme-bg-primary, #ffffff);
  color: var(--theme-text-primary, #323130);
  font-family: var(--theme-font-family, 'Segoe UI', sans-serif);
  font-size: var(--theme-font-size, 14px);
}

/* Header Styles */
.data-grid .rdg-header-row {
  background: var(--theme-bg-secondary, #f8f9fa);
  border-bottom: 1px solid var(--theme-border-primary, #d1d1d1);
  font-weight: 600;
}

.data-grid .rdg-cell {
  border-right: 1px solid var(--theme-border-primary, #d1d1d1);
  padding: 8px 12px;
  display: flex;
  align-items: center;
}

.data-grid .rdg-cell:last-child {
  border-right: none;
}

/* Row Styles */
.data-grid .rdg-row {
  background: var(--theme-bg-primary, #ffffff);
  border-bottom: 1px solid var(--theme-border-secondary, #e1e1e1);
}

.data-grid .rdg-row:hover {
  background: var(--theme-bg-hover, #f3f2f1);
}

.data-grid .rdg-row[aria-selected="true"] {
  background: var(--theme-bg-selected, #deecf9);
}

.data-grid .rdg-row[aria-selected="true"]:hover {
  background: var(--theme-bg-selected-hover, #c7e0f4);
}

/* Sort Indicator */
.data-grid .rdg-sort-arrow {
  color: var(--theme-primary, #0078d4);
}

/* Selection Column */
.data-grid .rdg-checkbox {
  accent-color: var(--theme-primary, #0078d4);
}

/* Focus Styles */
.data-grid .rdg-cell[tabindex="0"]:focus {
  outline: 2px solid var(--theme-primary, #0078d4);
  outline-offset: -2px;
}

/* Theme-specific overrides */

/* CRM Theme */
[data-theme="crm"] .data-grid {
  --theme-bg-primary: #ffffff;
  --theme-bg-secondary: #f3f2f1;
  --theme-bg-hover: #f3f2f1;
  --theme-bg-selected: #deecf9;
  --theme-bg-selected-hover: #c7e0f4;
  --theme-text-primary: #323130;
  --theme-text-secondary: #605e5c;
  --theme-border-primary: #8a8886;
  --theme-border-secondary: #c8c6c4;
  --theme-primary: #0078d4;
  --theme-border-radius: 2px;
  --theme-border-radius-sm: 1px;
  --theme-font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  --theme-font-size: 14px;
  --theme-font-size-sm: 12px;
}

/* MFE Theme */
[data-theme="mfe"] .data-grid {
  --theme-bg-primary: #ffffff;
  --theme-bg-secondary: #f8fafc;
  --theme-bg-hover: #f1f5f9;
  --theme-bg-selected: #e0e7ff;
  --theme-bg-selected-hover: #c7d2fe;
  --theme-text-primary: #1e293b;
  --theme-text-secondary: #64748b;
  --theme-border-primary: #cbd5e1;
  --theme-border-secondary: #e2e8f0;
  --theme-primary: #6366f1;
  --theme-border-radius: 8px;
  --theme-border-radius-sm: 4px;
  --theme-font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  --theme-font-size: 14px;
  --theme-font-size-sm: 12px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .data-grid__filter-cell {
    padding: 6px 8px;
  }
  
  .data-grid__filter-input {
    font-size: 14px;
    padding: 6px 8px;
  }
  
  .data-grid .rdg-cell {
    padding: 6px 8px;
    font-size: 13px;
  }
}

@media (max-width: 480px) {
  .data-grid__filter-row {
    flex-direction: column;
    height: auto;
  }
  
  .data-grid__filter-cell {
    border-right: none;
    border-bottom: 1px solid var(--theme-border-primary, #d1d1d1);
  }
  
  .data-grid__filter-cell:last-child {
    border-bottom: none;
  }
}
