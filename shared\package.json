{"name": "@crm/shared", "version": "1.0.0", "description": "Shared components, services, and utilities for CRM React apps", "main": "./dist/index.js", "module": "./dist/index.esm.js", "types": "./dist/index.d.ts", "exports": {".": {"import": "./dist/index.esm.js", "require": "./dist/index.js", "types": "./dist/index.d.ts"}, "./components": {"import": "./dist/components/index.esm.js", "require": "./dist/components/index.js", "types": "./dist/components/index.d.ts"}, "./services": {"import": "./dist/services/index.esm.js", "require": "./dist/services/index.js", "types": "./dist/services/index.d.ts"}, "./utils": {"import": "./dist/utils/index.esm.js", "require": "./dist/utils/index.js", "types": "./dist/utils/index.d.ts"}}, "files": ["dist", "components", "services", "utils"], "scripts": {"build": "tsc && vite build", "build:webresource": "cross-env VITE_DEPLOYMENT_MODE=web_resource npm run build", "build:standalone": "cross-env VITE_DEPLOYMENT_MODE=standalone npm run build", "dev": "vite build --watch", "type-check": "tsc --noEmit"}, "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0", "typescript": "^5.0.0"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@vitejs/plugin-react": "^4.2.0", "cross-env": "^7.0.3", "vite": "^5.0.8", "vite-plugin-dts": "^3.6.4"}, "dependencies": {"axios": "^1.6.2", "date-fns": "^2.30.0", "react-data-grid": "^7.0.0-beta.46"}, "optionalDependencies": {"@azure/msal-browser": "^3.5.0"}}